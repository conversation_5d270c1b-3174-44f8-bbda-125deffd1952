{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@public/*": ["public/*"], "@public": ["public"], "@layout/*": ["src/layout/*"], "@layout": ["src/layout"], "@components/*": ["src/components/*"], "@components": ["src/components"], "@common/*": ["src/components/common/*"], "@common": ["src/components/common"], "@screens/*": ["src/screens/*"], "@screens": ["src/screens"], "@utils/*": ["src/utils/*"], "@utils": ["src/utils"], "@hooks/*": ["src/hooks/*"], "@hooks": ["src/hooks"], "@services/*": ["src/services/*"], "@services": ["src/services"]}}}