# LingoTexts Environment Variables

# DeepL Translation Service (Recommended - High Quality)
# Get your free API key from: https://www.deepl.com/pro-api
EXPO_PUBLIC_DEEPL_API_KEY=your_deepl_api_key_here

# Google Translate Service (Alternative)
# Get your API key from: https://cloud.google.com/translate
EXPO_PUBLIC_GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here

# LibreTranslate Service (Optional - for custom instances)
# Only needed if using a private LibreTranslate instance with API key
EXPO_PUBLIC_LIBRETRANSLATE_API_KEY=your_libretranslate_api_key_here

# OpenAI Service (For advanced features)
# Get your API key from: https://platform.openai.com/api-keys
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration (if using external database)
DATABASE_URL=your_database_url_here

# App Configuration
APP_ENV=development
DEBUG_MODE=true
