{"expo": {"name": "LingoTexts", "slug": "LingoTexts", "version": "1.0.0", "orientation": "portrait", "icon": "./public/assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./public/assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "plugins": [["expo-document-picker", {"iCloudContainerEnvironment": "Production"}]], "ios": {"supportsTablet": true, "infoPlist": {"NSDocumentPickerUsageDescription": "Bu uygulama PDF dosyalarını seçmek için belge seçiciye erişim gerektirir.", "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "com.yusufakpinar.LingoTexts"}, "android": {"adaptiveIcon": {"foregroundImage": "./public/assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "infoPlist": {"NSDocumentPickerUsageDescription": "Bu uygulama PDF dosyalarını seçmek için belge seçiciye erişim gerektirir."}}, "web": {"favicon": "./public/assets/favicon.png"}, "extra": {"eas": {"projectId": "7bfae85b-0c06-4106-964b-c21ad7a024f2"}}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}