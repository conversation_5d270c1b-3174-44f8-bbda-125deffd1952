import { NavigatorScreenParams } from "@react-navigation/native";

export type RootStackParamList = {
    Auth: NavigatorScreenParams<AuthStackParamList>;
    Main: NavigatorScreenParams<MainTabParamList>;
};

export type AuthStackParamList = {
    Login: undefined;
    Register: undefined;
    ForgotPassword: undefined;
};

export type MainTabParamList = {
    Home: undefined;
    Reading: NavigatorScreenParams<ReadingStackParamList>;
    WordList: undefined;
    Quiz: undefined;
    Profile: undefined;
};

export type ReadingStackParamList = {
    ReadingHome: undefined;
    ReadingView: {
        bookTitle: string;
        bookId: string;
        bookContent: string;
        bookTotalPage: number;
        level?: string;
    };
    WordDetail: { word: string };
};

declare global {
    namespace ReactNavigation {
        interface RootParamList extends RootStackParamList {}
    }
}
