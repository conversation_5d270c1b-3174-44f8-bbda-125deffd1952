import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { ReadingStackParamList } from "../types/navigation";
import ReadingScreen from "../screens/main/ReadingScreen";
import ReadingViewScreen from "../screens/main/ReadingViewScreen";

const Stack = createStackNavigator<ReadingStackParamList>();

const ReadingNavigator: React.FC = () => {
    return (
        <Stack.Navigator
            screenOptions={{
                headerShown: false,
            }}
        >
            <Stack.Screen name="ReadingHome" component={ReadingScreen} />
            <Stack.Screen
                name="ReadingView"
                component={ReadingViewScreen}
                options={{
                    gestureEnabled: true,
                    gestureDirection: "horizontal",
                }}
            />
        </Stack.Navigator>
    );
};

export default ReadingNavigator;
