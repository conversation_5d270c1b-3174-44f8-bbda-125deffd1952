import React from 'react';
import {
    View,
    ScrollView,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    ViewStyle,
    ScrollViewProps,
    ColorValue,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export interface MainLayoutProps {
    children: React.ReactNode;
    scrollable?: boolean;
    showGradient?: boolean;
    gradientColors?: readonly [ColorValue, ColorValue, ...ColorValue[]];
    backgroundColor?: string;
    contentStyle?: ViewStyle;
    scrollViewProps?: ScrollViewProps;
    safeAreaStyle?: ViewStyle;
    paddingHorizontal?: number;
    paddingTop?: number;
    paddingBottom?: number;
}

const MainLayout: React.FC<MainLayoutProps> = ({
    children,
    scrollable = true,
    showGradient = false,
    gradientColors = ['#667eea', '#764ba2'],
    backgroundColor = '#f8f9fa',
    contentStyle,
    scrollViewProps,
    safeAreaStyle,
    paddingHorizontal = 20,
    paddingTop = 20,
    paddingBottom = 100, // Extra space for bottom navigation
}) => {
    const containerStyle = [
        styles.container,
        { backgroundColor: showGradient ? 'transparent' : backgroundColor },
        safeAreaStyle,
    ];

    const contentContainerStyle = [
        styles.content,
        {
            paddingHorizontal,
            paddingTop,
            paddingBottom,
        },
        contentStyle,
    ];

    const renderContent = () => {
        if (scrollable) {
            return (
                <ScrollView
                    style={styles.scrollView}
                    contentContainerStyle={contentContainerStyle}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    {...scrollViewProps}
                >
                    {children}
                </ScrollView>
            );
        }

        return (
            <View style={contentContainerStyle}>
                {children}
            </View>
        );
    };

    if (showGradient) {
        return (
            <>
                <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
                <LinearGradient
                    colors={gradientColors}
                    style={styles.gradientContainer}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                >
                    <SafeAreaView style={containerStyle}>
                        {renderContent()}
                    </SafeAreaView>
                </LinearGradient>
            </>
        );
    }

    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={backgroundColor} />
            <SafeAreaView style={containerStyle}>
                {renderContent()}
            </SafeAreaView>
        </>
    );
};

const styles = StyleSheet.create({
    gradientContainer: {
        flex: 1,
    },
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        flexGrow: 1,
    },
});

export default MainLayout;
