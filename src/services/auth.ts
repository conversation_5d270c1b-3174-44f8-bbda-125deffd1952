import { createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, User, UserCredential } from 'firebase/auth';
import { auth } from './firebase';

export interface AuthService {
    signUp: (email: string, password: string) => Promise<UserCredential>;
    signIn: (email: string, password: string) => Promise<UserCredential>;
    signOut: () => Promise<void>;
    getCurrentUser: () => User | null;
}

export const authService: AuthService = {
    signUp: async (email: string, password: string): Promise<UserCredential> => {
        return await createUserWithEmailAndPassword(auth, email, password);
    },

    signIn: async (email: string, password: string): Promise<UserCredential> => {
        return await signInWithEmailAndPassword(auth, email, password);
    },

    signOut: async (): Promise<void> => {
        return await signOut(auth);
    },

    getCurrentUser: (): User | null => {
        return auth.currentUser;
    }
};
