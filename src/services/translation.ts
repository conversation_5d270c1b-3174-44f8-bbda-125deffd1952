// Translation service using OpenAI and Google Translate APIs

export interface TranslationResult {
    word: string;
    meaning: string;
    pronunciation?: string;
    partOfSpeech?: string;
    examples: string[];
    synonyms?: string[];
    difficulty?: "beginner" | "intermediate" | "advanced";
}

export interface TranslationService {
    translateWord: (word: string, targetLanguage?: string) => Promise<TranslationResult>;
    translateText: (text: string, targetLanguage?: string) => Promise<string>;
    getWordDefinition: (word: string) => Promise<TranslationResult>;
}

class OpenAITranslationService implements TranslationService {
    private apiKey: string;
    private baseUrl = "https://api.openai.com/v1/chat/completions";

    constructor() {
        this.apiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY || "";
    }

    async translateWord(word: string, targetLanguage = "Turkish"): Promise<TranslationResult> {
        if (!this.apiKey) {
            throw new Error("OpenAI API key not configured");
        }

        const prompt = `
      Translate the English word "${word}" to ${targetLanguage} and provide detailed information.
      Return the response in the following JSON format:
      {
        "word": "${word}",
        "meaning": "Turkish translation",
        "pronunciation": "phonetic pronunciation",
        "partOfSpeech": "noun/verb/adjective/etc",
        "examples": ["example sentence 1", "example sentence 2"],
        "synonyms": ["synonym1", "synonym2"],
        "difficulty": "beginner/intermediate/advanced"
      }
    `;

        try {
            const response = await fetch(this.baseUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    model: "gpt-3.5-turbo",
                    messages: [
                        {
                            role: "system",
                            content:
                                "You are a helpful language learning assistant. Always respond with valid JSON.",
                        },
                        {
                            role: "user",
                            content: prompt,
                        },
                    ],
                    max_tokens: 500,
                    temperature: 0.3,
                }),
            });

            if (!response.ok) {
                throw new Error(`OpenAI API error: ${response.status}`);
            }

            const data = await response.json();
            const content = data.choices[0]?.message?.content;

            if (!content) {
                throw new Error("No response from OpenAI");
            }

            return JSON.parse(content);
        } catch (error) {
            console.error("OpenAI translation error:", error);
            // Fallback to mock data for development
            return this.getMockTranslation(word);
        }
    }

    async translateText(text: string, targetLanguage = "Turkish"): Promise<string> {
        if (!this.apiKey) {
            throw new Error("OpenAI API key not configured");
        }

        const prompt = `Translate the following English text to ${targetLanguage}: "${text}"`;

        try {
            const response = await fetch(this.baseUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    model: "gpt-3.5-turbo",
                    messages: [
                        {
                            role: "system",
                            content:
                                "You are a professional translator. Provide only the translation without any additional text.",
                        },
                        {
                            role: "user",
                            content: prompt,
                        },
                    ],
                    max_tokens: 200,
                    temperature: 0.1,
                }),
            });

            if (!response.ok) {
                throw new Error(`OpenAI API error: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0]?.message?.content || text;
        } catch (error) {
            console.error("OpenAI text translation error:", error);
            return `[Çeviri hatası: ${text}]`;
        }
    }

    async getWordDefinition(word: string): Promise<TranslationResult> {
        return this.translateWord(word);
    }

    private getMockTranslation(word: string): TranslationResult {
        // Mock data for development/testing
        const mockTranslations: { [key: string]: TranslationResult } = {
            serendipity: {
                word: "serendipity",
                meaning: "tesadüf, şans eseri karşılaşma",
                pronunciation: "/ˌserənˈdipədē/",
                partOfSpeech: "noun",
                examples: [
                    "It was pure serendipity that we met at the coffee shop.",
                    "The discovery was a result of serendipity rather than planning.",
                ],
                synonyms: ["chance", "fortune", "luck"],
                difficulty: "intermediate",
            },
            ephemeral: {
                word: "ephemeral",
                meaning: "geçici, kısa süreli",
                pronunciation: "/əˈfem(ə)rəl/",
                partOfSpeech: "adjective",
                examples: [
                    "The beauty of cherry blossoms is ephemeral.",
                    "Social media trends are often ephemeral.",
                ],
                synonyms: ["temporary", "fleeting", "transient"],
                difficulty: "advanced",
            },
        };

        return (
            mockTranslations[word.toLowerCase()] || {
                word,
                meaning: `[${word} için çeviri bulunamadı]`,
                examples: [`Example sentence with ${word}.`],
                difficulty: "intermediate",
            }
        );
    }
}

class GoogleTranslateService implements TranslationService {
    private apiKey: string;
    private baseUrl = "https://translation.googleapis.com/language/translate/v2";

    constructor() {
        this.apiKey = process.env.EXPO_PUBLIC_GOOGLE_TRANSLATE_API_KEY || "";
    }

    async translateWord(word: string, targetLanguage = "tr"): Promise<TranslationResult> {
        const translation = await this.translateText(word, targetLanguage);

        return {
            word,
            meaning: translation,
            examples: [`Example sentence with ${word}.`],
            difficulty: "intermediate",
        };
    }

    async translateText(text: string, targetLanguage = "tr"): Promise<string> {
        if (!this.apiKey) {
            throw new Error("Google Translate API key not configured");
        }

        try {
            const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    q: text,
                    target: targetLanguage,
                    source: "en",
                }),
            });

            if (!response.ok) {
                throw new Error(`Google Translate API error: ${response.status}`);
            }

            const data = await response.json();
            return data.data.translations[0].translatedText;
        } catch (error) {
            console.error("Google Translate error:", error);
            return `[Çeviri hatası: ${text}]`;
        }
    }

    async getWordDefinition(word: string): Promise<TranslationResult> {
        return this.translateWord(word);
    }
}

class LibreTranslateService implements TranslationService {
    private baseUrl: string;
    private apiKey?: string;

    constructor(baseUrl = "https://libretranslate.com", apiKey?: string) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey || process.env.EXPO_PUBLIC_LIBRETRANSLATE_API_KEY;
    }

    async translateWord(word: string, targetLanguage = "tr"): Promise<TranslationResult> {
        const translation = await this.translateText(word, targetLanguage);

        return {
            word,
            meaning: translation,
            examples: [`Example sentence with ${word}.`],
            difficulty: "intermediate",
        };
    }

    async translateText(text: string, targetLanguage = "tr"): Promise<string> {
        try {
            const requestBody: any = {
                q: text,
                source: "en",
                target: targetLanguage,
                format: "text",
            };

            // API key varsa ekle
            if (this.apiKey) {
                requestBody.api_key = this.apiKey;
            }

            const response = await fetch(`${this.baseUrl}/translate`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                throw new Error(`LibreTranslate API error: ${response.status}`);
            }

            const data = await response.json();
            return data.translatedText || `[Çeviri hatası: ${text}]`;
        } catch (error) {
            console.error("LibreTranslate error:", error);
            return `[Çeviri hatası: ${text}]`;
        }
    }

    async getWordDefinition(word: string): Promise<TranslationResult> {
        return this.translateWord(word);
    }

    // LibreTranslate desteklenen dilleri getir
    async getSupportedLanguages(): Promise<Array<{ code: string; name: string }>> {
        try {
            const url = this.apiKey
                ? `${this.baseUrl}/languages?api_key=${this.apiKey}`
                : `${this.baseUrl}/languages`;

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`LibreTranslate languages API error: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error("LibreTranslate languages error:", error);
            return [
                { code: "en", name: "English" },
                { code: "tr", name: "Turkish" },
                { code: "es", name: "Spanish" },
                { code: "fr", name: "French" },
                { code: "de", name: "German" },
            ];
        }
    }
}

class DeepLTranslationService implements TranslationService {
    private apiKey: string;
    private baseUrl = "https://api-free.deepl.com/v2"; // Free tier URL
    private isPro: boolean;

    constructor(apiKey?: string, isPro = false) {
        this.apiKey = apiKey || process.env.EXPO_PUBLIC_DEEPL_API_KEY || "";
        this.isPro = isPro;
        // Pro tier kullanıcıları için farklı URL
        if (this.isPro) {
            this.baseUrl = "https://api.deepl.com/v2";
        }
    }

    async translateWord(word: string, targetLanguage = "TR"): Promise<TranslationResult> {
        const translation = await this.translateText(word, targetLanguage);

        return {
            word,
            meaning: translation,
            examples: [`Example sentence with ${word}.`],
            difficulty: "intermediate",
        };
    }

    async translateText(text: string, targetLanguage = "TR"): Promise<string> {
        if (!this.apiKey) {
            throw new Error("DeepL API key not configured");
        }

        try {
            const response = await fetch(`http://localhost:8085/api/translate`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    text,
                    source_lang: "EN",
                    target_lang: targetLanguage,
                    // formality: "default", // default, more, less
                }),
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(
                    `DeepL API error: ${response.status} - ${errorData.message || "Unknown error"}`
                );
            }

            const data = await response.json();
            return data.translations[0]?.text || `[Çeviri hatası: ${text}]`;
        } catch (error) {
            console.error("DeepL translation error:", error);
            return `[Çeviri hatası: ${text}]`;
        }
    }

    async getWordDefinition(word: string): Promise<TranslationResult> {
        return this.translateWord(word);
    }

    // DeepL desteklenen dilleri getir
    async getSupportedLanguages(): Promise<Array<{ code: string; name: string }>> {
        if (!this.apiKey) {
            // API key yoksa varsayılan dilleri döndür
            return [
                { code: "EN", name: "English" },
                { code: "TR", name: "Turkish" },
                { code: "DE", name: "German" },
                { code: "FR", name: "French" },
                { code: "ES", name: "Spanish" },
                { code: "IT", name: "Italian" },
                { code: "PT", name: "Portuguese" },
                { code: "RU", name: "Russian" },
                { code: "JA", name: "Japanese" },
                { code: "ZH", name: "Chinese" },
            ];
        }

        try {
            const response = await fetch(`${this.baseUrl}/languages?type=target`, {
                method: "GET",
                headers: {
                    Authorization: `DeepL-Auth-Key ${this.apiKey}`,
                },
            });

            if (!response.ok) {
                throw new Error(`DeepL languages API error: ${response.status}`);
            }

            const data = await response.json();
            return data.map((lang: any) => ({
                code: lang.language,
                name: lang.name,
            }));
        } catch (error) {
            console.error("DeepL languages error:", error);
            // Fallback to default languages
            return [
                { code: "EN", name: "English" },
                { code: "TR", name: "Turkish" },
                { code: "DE", name: "German" },
                { code: "FR", name: "French" },
                { code: "ES", name: "Spanish" },
            ];
        }
    }

    // API kullanım bilgilerini getir
    async getUsage(): Promise<{ character_count: number; character_limit: number }> {
        if (!this.apiKey) {
            throw new Error("DeepL API key not configured");
        }

        try {
            const response = await fetch(`${this.baseUrl}/usage`, {
                method: "GET",
                headers: {
                    Authorization: `DeepL-Auth-Key ${this.apiKey}`,
                },
            });

            if (!response.ok) {
                throw new Error(`DeepL usage API error: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error("DeepL usage error:", error);
            return { character_count: 0, character_limit: 500000 };
        }
    }
}

// Export the service instances
export const translationService: TranslationService = new OpenAITranslationService();
export const googleTranslateService: TranslationService = new GoogleTranslateService();
export const libreTranslateService: TranslationService = new LibreTranslateService();
export const deepLTranslationService: TranslationService = new DeepLTranslationService();

// Alternative LibreTranslate instances for fallback
export const libreTranslateServiceAlt1: TranslationService = new LibreTranslateService(
    "https://translate.argosopentech.com"
);
export const libreTranslateServiceAlt2: TranslationService = new LibreTranslateService(
    "https://libretranslate.com"
);

// Smart translation service with fallback mechanism
class SmartTranslationService implements TranslationService {
    private services: TranslationService[] = [
        deepLTranslationService, // En kaliteli çeviri
        googleTranslateService, // Güvenilir alternatif
        libreTranslateService, // Ücretsiz seçenek
        libreTranslateServiceAlt1, // Yedek LibreTranslate
        libreTranslateServiceAlt2, // Yedek LibreTranslate 2
    ];

    async translateWord(word: string, targetLanguage = "tr"): Promise<TranslationResult> {
        for (const service of this.services) {
            try {
                const result = await service.translateWord(word, targetLanguage);
                // Başarılı çeviri kontrolü
                if (result.meaning && !result.meaning.includes("Çeviri hatası")) {
                    return result;
                }
            } catch (error) {
                console.warn(`Translation service failed, trying next...`, error);
                continue;
            }
        }

        // Tüm servisler başarısız olursa mock data döndür
        return {
            word,
            meaning: `[${word} için çeviri bulunamadı]`,
            examples: [`Example sentence with ${word}.`],
            difficulty: "intermediate",
        };
    }

    async translateText(text: string, targetLanguage = "tr"): Promise<string> {
        for (const service of this.services) {
            try {
                const result = await service.translateText(text, targetLanguage);
                if (result && !result.includes("Çeviri hatası")) {
                    return result;
                }
            } catch (error) {
                console.warn(`Translation service failed, trying next...`, error);
                continue;
            }
        }

        return `[Çeviri hatası: ${text}]`;
    }

    async getWordDefinition(word: string): Promise<TranslationResult> {
        return this.translateWord(word);
    }
}

// Smart service instance
export const smartTranslationService: TranslationService = new SmartTranslationService();

// Utility function for easy access - now uses DeepL as primary
export const translateWord = async (word: string): Promise<TranslationResult> => {
    return deepLTranslationService.translateWord(word);
};
