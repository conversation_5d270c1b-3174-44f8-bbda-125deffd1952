import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

export interface PDFProcessingResult {
    success: boolean;
    content?: string;
    pageCount?: number;
    wordCount?: number;
    error?: string;
}

export interface PDFUploadProgress {
    stage: 'selecting' | 'reading' | 'processing' | 'uploading' | 'completed' | 'error';
    progress: number; // 0-100
    message: string;
}

export class PDFProcessorService {
    private readonly MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
    private readonly SUPPORTED_TYPES = ['application/pdf'];

    /**
     * Pick and validate a PDF file from the device
     */
    async pickPDFFile(): Promise<DocumentPicker.DocumentPickerResult | null> {
        try {
            console.log('Starting PDF file picker...');

            const result = await DocumentPicker.getDocumentAsync({
                type: ['application/pdf', '*/*'],
                copyToCacheDirectory: true,
                multiple: false,
            });

             console.log('Document picker result:', result);

            if (result.canceled) {
                return null;
            }

            
            const file = result.assets[0];
            console.log('Document picker result:', this.validateFile(file));
            
            // Validate file
            if (!this.validateFile(file)) {
                return null;
            }

            console.log('Document picker result validated:', file);

            return result;
        } catch (error) {
            console.error('Error picking PDF file:', error);
            Alert.alert('Hata', 'Dosya seçilirken bir hata oluştu.');
            return null;
        }
    }

    /**
     * Validate the selected PDF file
     */
    private validateFile(file: DocumentPicker.DocumentPickerAsset): boolean {
        console.log('Validating file check:', this.SUPPORTED_TYPES.includes(file.mimeType || ''));

        // Check file type
        if (!this.SUPPORTED_TYPES.includes(file.mimeType || '')) {
            Alert.alert('Geçersiz Dosya', 'Lütfen sadece PDF dosyası seçin.');
            return false;
        }

        console.log('Validating file size:', file.size && file.size > this.MAX_FILE_SIZE);


        // Check file size
        if (file.size && file.size > this.MAX_FILE_SIZE) {
            Alert.alert(
                'Dosya Çok Büyük', 
                `Dosya boyutu ${this.MAX_FILE_SIZE / (20 * 1024 * 1024)}MB'dan küçük olmalıdır.`
            );
            return false;
        }

        return true;
    }

    /**
     * Process PDF file and extract text content
     * Note: This is a simplified implementation. In a real app, you would use
     * a proper PDF parsing library like react-native-pdf or pdf-parse
     */
    async processPDFFile(
        file: DocumentPicker.DocumentPickerAsset,
        onProgress?: (progress: PDFUploadProgress) => void
    ): Promise<PDFProcessingResult> {
        try {
            onProgress?.({
                stage: 'reading',
                progress: 10,
                message: 'PDF dosyası okunuyor...'
            });

            // Read file content
            const fileContent = await FileSystem.readAsStringAsync(file.uri, {
                encoding: FileSystem.EncodingType.Base64,
            });

            onProgress?.({
                stage: 'processing',
                progress: 50,
                message: 'Metin çıkarılıyor...'
            });

            // Simulate PDF text extraction
            // In a real implementation, you would use a PDF parsing library
            const extractedText = await this.extractTextFromPDF(fileContent);

            onProgress?.({
                stage: 'processing',
                progress: 80,
                message: 'Metin işleniyor...'
            });

            // Process and clean the text
            const processedText = this.cleanAndFormatText(extractedText);
            const wordCount = this.countWords(processedText);
            const pageCount = this.estimatePageCount(processedText);

            onProgress?.({
                stage: 'completed',
                progress: 100,
                message: 'İşlem tamamlandı!'
            });

            return {
                success: true,
                content: processedText,
                pageCount,
                wordCount,
            };

        } catch (error) {
            console.error('Error processing PDF:', error);
            onProgress?.({
                stage: 'error',
                progress: 0,
                message: 'PDF işlenirken hata oluştu.'
            });

            return {
                success: false,
                error: 'PDF dosyası işlenirken bir hata oluştu.',
            };
        }
    }

    /**
     * Extract text from PDF (simplified implementation)
     * In a real app, you would use a proper PDF parsing library
     */
    private async extractTextFromPDF(base64Content: string): Promise<string> {
        // This is a placeholder implementation
        // In a real app, you would use libraries like:
        // - react-native-pdf
        // - pdf-parse
        // - or a cloud service like Google Cloud Document AI
        
        // For demo purposes, return sample text
        return `Bu bir örnek PDF metnidir. Gerçek uygulamada, PDF dosyasından metin çıkarma işlemi burada yapılacaktır.

Bu metin, PDF dosyasından çıkarılan içeriği temsil eder. Paragraflar ve satır sonları korunmuştur.

İkinci paragraf burada başlar. PDF işleme kütüphanesi kullanılarak gerçek metin çıkarma işlemi yapılabilir.

Üçüncü paragraf da mevcuttur. Bu şekilde kitap formatında metin elde edilir.`;
    }

    /**
     * Clean and format extracted text
     */
    private cleanAndFormatText(text: string): string {
        return text
            // Remove excessive whitespace
            .replace(/\s+/g, ' ')
            // Normalize line breaks
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            // Remove multiple consecutive line breaks (keep max 2 for paragraphs)
            .replace(/\n{3,}/g, '\n\n')
            // Trim whitespace
            .trim();
    }

    /**
     * Count words in text
     */
    private countWords(text: string): number {
        return text.split(/\s+/).filter(word => word.length > 0).length;
    }

    /**
     * Estimate page count based on word count
     */
    private estimatePageCount(text: string): number {
        const wordCount = this.countWords(text);
        // Assume approximately 250 words per page
        return Math.max(1, Math.ceil(wordCount / 250));
    }

    /**
     * Generate a clean title from filename
     */
    generateTitleFromFilename(filename: string): string {
        return filename
            .replace(/\.[^/.]+$/, '') // Remove extension
            .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
            .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize first letter of each word
            .trim();
    }

    /**
     * Auto-detect difficulty level based on text complexity
     * This is a simplified implementation
     */
    detectDifficultyLevel(text: string): "A1" | "A2" | "B1" | "B2" | "C1" | "C2" {
        const wordCount = this.countWords(text);
        const avgWordLength = text.replace(/\s+/g, '').length / wordCount;
        const sentenceCount = text.split(/[.!?]+/).length;
        const avgSentenceLength = wordCount / sentenceCount;

        // Simple heuristic based on text complexity
        if (avgWordLength < 4 && avgSentenceLength < 10) {
            return "A1";
        } else if (avgWordLength < 5 && avgSentenceLength < 15) {
            return "A2";
        } else if (avgWordLength < 6 && avgSentenceLength < 20) {
            return "B1";
        } else if (avgWordLength < 7 && avgSentenceLength < 25) {
            return "B2";
        } else if (avgWordLength < 8 && avgSentenceLength < 30) {
            return "C1";
        } else {
            return "C2";
        }
    }
}

export const pdfProcessorService = new PDFProcessorService();
