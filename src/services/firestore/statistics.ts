import { wordService } from "./words";
import { QuizResult, quizService } from "./quiz";
import { readingService } from "./reading";

export class StatisticsService {
  async getUserStats(userId: string) {
          const [words, quizResults, readingSessions] = await Promise.all([
              wordService.getUserWords(userId),
              quizService.getUserQuizResults(userId, 100),
              readingService.getUserReadingSessions(userId),
          ]);
  
          const totalWords = words.length;
          const learnedWords = words.filter((word) => word.learned).length;
          const totalQuizzes = quizResults.length;
          const averageScore =
              quizResults.length > 0
                  ? (quizResults.reduce(
                        (sum, result) => sum + result.score / result.totalQuestions,
                        0
                    ) /
                        quizResults.length) *
                    100
                  : 0;
  
          const totalReadingTime = readingSessions
              .filter((session) => session.endTime)
              .reduce((total, session) => {
                  const duration = session.endTime!.seconds - session.startTime.seconds;
                  return total + duration;
              }, 0);
  
          return {
              totalWords,
              learnedWords,
              learningWords: totalWords - learnedWords,
              totalQuizzes,
              averageScore: Math.round(averageScore),
              totalReadingTime: Math.round(totalReadingTime / 60), // in minutes
              currentStreak: this.calculateStreak(quizResults),
          };
      }
  
      private calculateStreak(quizResults: QuizResult[]): number {
          if (quizResults.length === 0) return 0;
  
          let streak = 0;
          const today = new Date();
          today.setHours(0, 0, 0, 0);
  
          const sortedResults = quizResults.sort(
              (a, b) => b.completedAt.seconds - a.completedAt.seconds
          );
  
          for (const result of sortedResults) {
              const resultDate = new Date(result.completedAt.seconds * 1000);
              resultDate.setHours(0, 0, 0, 0);
  
              const daysDiff = Math.floor(
                  (today.getTime() - resultDate.getTime()) / (1000 * 60 * 60 * 24)
              );
  
              if (daysDiff === streak) {
                  streak++;
              } else {
                  break;
              }
          }
  
          return streak;
      }
}

export const statisticsService = new StatisticsService();