import { Timestamp, collection, addDoc, updateDoc, deleteDoc, doc, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from './../firebase';

export interface UserBook {
    id?: string;
    userId: string;
    title: string;
    difficulty: 'A1' | 'A2' | 'B1' | 'B2' | 'C1' | 'C2';
    pages: number;
    rating?: number;
    content: string;
    isUserUploaded: true;
    uploadDate: Timestamp;
    originalFilename: string;
    processingStatus: 'processing' | 'completed' | 'failed';
    fileSize?: number;
    wordCount?: number;
    lastReadDate?: Timestamp;
    readingProgress?: number;
}

export class BookService {
    async addUserBook(bookData: Omit<UserBook, 'id'>): Promise<string> {
        const docRef = await addDoc(collection(db, 'userBooks'), bookData);
        return docRef.id;
    }

    async getUserBooks(userId: string): Promise<UserBook[]> {
        try {
            console.log('getUserBooks called with userId:', userId);

            // First try with orderBy
            try {
                const q = query(
                    collection(db, 'userBooks'),
                    where('userId', '==', userId),
                    orderBy('uploadDate', 'desc')
                );
                const querySnapshot = await getDocs(q);
                console.log('getUserBooks success with orderBy, docs count:', querySnapshot.docs.length);

                return querySnapshot.docs.map((doc) => ({
                    id: doc.id,
                    ...doc.data()
                } as UserBook));

            } catch (orderByError) {
                console.log('OrderBy failed, trying without orderBy:', orderByError);

                // Fallback: query without orderBy and sort in memory
                const q = query(
                    collection(db, 'userBooks'),
                    where('userId', '==', userId)
                );
                const querySnapshot = await getDocs(q);
                console.log('getUserBooks success without orderBy, docs count:', querySnapshot.docs.length);

                const books = querySnapshot.docs.map((doc) => ({
                    id: doc.id,
                    ...doc.data()
                } as UserBook));

                // Sort in memory by uploadDate
                return books.sort((a, b) => {
                    const aDate = a.uploadDate?.toDate?.() || new Date(0);
                    const bDate = b.uploadDate?.toDate?.() || new Date(0);
                    return bDate.getTime() - aDate.getTime();
                });
            }

        } catch (error) {
            console.error('getUserBooks error:', error);
            throw new Error(`Kitaplar yüklenirken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
        }
    }

    async updateUserBook(bookId: string, updates: Partial<UserBook>): Promise<void> {
        const bookRef = doc(db, 'userBooks', bookId);
        await updateDoc(bookRef, updates);
    }

    async deleteUserBook(bookId: string): Promise<void> {
        await deleteDoc(doc(db, 'userBooks', bookId));
    }

    async getUserBookById(bookId: string): Promise<UserBook | null> {
        const q = query(collection(db, 'userBooks'), where('__name__', '==', bookId));
        const snapshot = await getDocs(q);

        if (!snapshot.empty) {
            const docSnap = snapshot.docs[0];
            return {
                id: docSnap.id,
                ...docSnap.data()
            } as UserBook;
        }

        return null;
    }

    async updateBookProgress(bookId: string, progress: number): Promise<void> {
        await this.updateUserBook(bookId, {
            readingProgress: progress,
            lastReadDate: Timestamp.now()
        });
    }
}

export const bookService = new BookService();
