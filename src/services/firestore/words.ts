import {
    collection,
    doc,
    addDoc,
    updateDoc,
    deleteDoc,
    getDocs,
    query,
    where,
    orderBy,
    Timestamp,
} from "firebase/firestore";
import { db } from "../firebase";
import { TranslationResult } from "../translation";

// Data types
export interface UserWord {
    id?: string;
    userId: string;
    word: string;
    meaning: string;
    pronunciation?: string;
    partOfSpeech?: string;
    examples: string[];
    synonyms?: string[];
    difficulty?: "beginner" | "intermediate" | "advanced";
    learned: boolean;
    createdAt: Timestamp;
    lastReviewed?: Timestamp;
    reviewCount: number;
}

// Firestore service class
export class WordService {
    // User Words
    async addUserWord(userId: string, translationResult: TranslationResult): Promise<string> {
        const userWord: Omit<UserWord, "id"> = {
            userId,
            word: translationResult.word,
            meaning: translationResult.meaning,
            pronunciation: translationResult.pronunciation,
            partOfSpeech: translationResult.partOfSpeech,
            examples: translationResult.examples,
            synonyms: translationResult.synonyms,
            difficulty: translationResult.difficulty,
            learned: false,
            createdAt: Timestamp.now(),
            reviewCount: 0,
        };

        const docRef = await addDoc(collection(db, "userWords"), userWord);
        return docRef.id;
    }

    // Add word directly from modal form
    async addUserWordDirect(
        userId: string,
        wordData: {
            word: string;
            meaning: string;
            pronunciation?: string;
            partOfSpeech: string;
            examples: string[];
            synonyms: string[];
            difficulty: "beginner" | "intermediate" | "advanced";
        }
    ): Promise<string> {
        const userWord: Omit<UserWord, "id"> = {
            userId,
            word: wordData.word,
            meaning: wordData.meaning,
            pronunciation: wordData.pronunciation,
            partOfSpeech: wordData.partOfSpeech,
            examples: wordData.examples,
            synonyms: wordData.synonyms,
            difficulty: wordData.difficulty,
            learned: false,
            createdAt: Timestamp.now(),
            reviewCount: 0,
        };

        const docRef = await addDoc(collection(db, "userWords"), userWord);
        return docRef.id;
    }

    async getUserWords(userId: string): Promise<UserWord[]> {
        console.log("getUserWords", userId);
        const q = query(
            collection(db, "userWords"),
            where("userId", "==", userId),
            orderBy("createdAt", "desc")
        );

        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(
            (doc) =>
                ({
                    id: doc.id,
                    ...doc.data(),
                } as UserWord)
        );
    }

    async updateUserWord(wordId: string, updates: Partial<UserWord>): Promise<void> {
        const wordRef = doc(db, "userWords", wordId);
        await updateDoc(wordRef, updates);
    }

    async deleteUserWord(wordId: string): Promise<void> {
        await deleteDoc(doc(db, "userWords", wordId));
    }

    async markWordAsLearned(wordId: string): Promise<void> {
        await this.updateUserWord(wordId, {
            learned: true,
            lastReviewed: Timestamp.now(),
        });
    }
}

export const wordService = new WordService();