import {
  Timestamp,
  collection,
  addDoc,
  updateDoc,
  doc,
  getDocs,
  query,
  where,
  orderBy
} from "firebase/firestore";
import { db } from "../firebase";

export interface ReadingSession {
    id?: string;
    userId: string;
    documentName: string;
    documentId: string;
    startTime: Timestamp;
    endTime?: Timestamp;
    wordsLearned: string[];
    progress: number; // percentage
}

export class ReadingService {
  // Reading Sessions
      async startReadingSession(
          userId: string,
          documentName: string,
          documentId: string
      ): Promise<string> {
          const session: Omit<ReadingSession, "id"> = {
              userId,
              documentName,
              documentId,
              startTime: Timestamp.now(),
              wordsLearned: [],
              progress: 0,
          };
  
          const docRef = await addDoc(collection(db, "readingSessions"), session);
          return docRef.id;
      }
  
      async updateReadingSession(sessionId: string, updates: Partial<ReadingSession>): Promise<void> {
          const sessionRef = doc(db, "readingSessions", sessionId);
          await updateDoc(sessionRef, updates);
      }
  
      async endReadingSession(
          sessionId: string,
          wordsLearned: string[],
          progress: number
      ): Promise<void> {
          await this.updateReadingSession(sessionId, {
              endTime: Timestamp.now(),
              wordsLearned,
              progress,
          });
      }
  
      async getUserReadingSessions(userId: string): Promise<ReadingSession[]> {
          const q = query(
              collection(db, "readingSessions"),
              where("userId", "==", userId),
              orderBy("startTime", "desc")
          );
  
          const querySnapshot = await getDocs(q);
          return querySnapshot.docs.map(
              (doc) =>
                  ({
                      id: doc.id,
                      ...doc.data(),
                  } as ReadingSession)
          );
      }
}

export const readingService = new ReadingService();