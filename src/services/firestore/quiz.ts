import {
  Timestamp,
  collection,
  addDoc,
  limit,
  getDocs,
  query,
  where,
  orderBy
} from "firebase/firestore";
import { db } from "../firebase";

export interface QuizResult {
    id?: string;
    userId: string;
    score: number;
    totalQuestions: number;
    quizType: string;
    completedAt: Timestamp;
    timeSpent: number; // in seconds
    wordsQuizzed: string[];
}

export class QuizService {
  async saveQuizResult(quizResult: Omit<QuizResult, "id">): Promise<string> {
          const docRef = await addDoc(collection(db, "quizResults"), quizResult);
          return docRef.id;
      }
  
      async getUserQuizResults(userId: string, limitCount = 10): Promise<QuizResult[]> {
          const q = query(
              collection(db, "quizResults"),
              where("userId", "==", userId),
              orderBy("completedAt", "desc"),
              limit(limitCount)
          );
  
          const querySnapshot = await getDocs(q);
          return querySnapshot.docs.map(
              (doc) =>
                  ({
                      id: doc.id,
                      ...doc.data(),
                  } as QuizResult)
          );
      }
}

export const quizService = new QuizService();