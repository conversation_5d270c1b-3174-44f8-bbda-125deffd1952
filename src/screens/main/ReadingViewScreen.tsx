import React, { useState } from "react";
import { View, ScrollView, StyleSheet, SafeAreaView, StatusBar } from "react-native";

import { StackNavigationProp } from "@react-navigation/stack";
import { RouteProp } from "@react-navigation/native";
import { ReadingStackParamList } from "../../types/navigation";
import { useHideTabNavigation } from "../../hooks/useHideTabNavigation";
import {
    HeaderButton,
    NavigationButton,
    Typography,
    SpeedDial,
    SpeedDialAction,
    ColorPicker,
    ColorOption,
    Loading,
} from "../../components/ui";
import InteractiveText from "../../components/reading/InteractiveText";
import { Ionicons } from "@expo/vector-icons";

type ReadingViewScreenNavigationProp = StackNavigationProp<ReadingStackParamList, "ReadingView">;
type ReadingViewScreenRouteProp = RouteProp<ReadingStackParamList, "ReadingView">;

interface ReadingViewScreenProps {
    navigation: ReadingViewScreenNavigationProp;
    route: ReadingViewScreenRouteProp;
}

const ReadingViewScreen: React.FC<ReadingViewScreenProps> = ({ navigation, route }) => {
    const { bookTitle, bookContent, bookTotalPage } = route.params;
    const [currentPage, setCurrentPage] = useState(1);

    // Reading settings state
    const [fontSize, setFontSize] = useState(16);
    const [backgroundColor, setBackgroundColor] = useState("#ffffff");
    const [textColor, setTextColor] = useState("#374151");
    const [isColorPickerVisible, setIsColorPickerVisible] = useState(false);
    const [bookLoading, setBookLoading] = useState(true);

    // Constants for settings
    const fontSizes = [14, 16, 18, 20, 22];
    const backgroundColors: ColorOption[] = [
        { color: "#ffffff", name: "Beyaz", textColor: "#374151" },
        { color: "#f8f9fa", name: "Açık Gri", textColor: "#374151" },
        { color: "#fff8e1", name: "Krem", textColor: "#374151" },
        { color: "#f3e5f5", name: "Lavanta", textColor: "#374151" },
        { color: "#e8f5e8", name: "Açık Yeşil", textColor: "#374151" },
        { color: "#1f2937", name: "Koyu Mod", textColor: "#f9fafb" },
        { color: "#fef3c7", name: "Sepia", textColor: "#92400e" },
    ];

    // Hide main tab navigation when this screen is focused
    useHideTabNavigation({ navigation });

    const handleGoBack = () => {
        navigation.goBack();
    };

    const handlePageTurn = (direction: "next" | "prev") => {
        if (direction === "next" && currentPage < (bookTotalPage || 1)) {
            setCurrentPage(currentPage + 1);
        } else if (direction === "prev" && currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    // Font size control functions
    const increaseFontSize = () => {
        const currentIndex = fontSizes.indexOf(fontSize);
        if (currentIndex < fontSizes.length - 1) {
            setFontSize(fontSizes[currentIndex + 1]);
        }
    };

    const decreaseFontSize = () => {
        const currentIndex = fontSizes.indexOf(fontSize);
        if (currentIndex > 0) {
            setFontSize(fontSizes[currentIndex - 1]);
        }
    };

    // Speed dial actions
    const speedDialActions: SpeedDialAction[] = [
        {
            icon: "remove",
            label: "Font Küçült",
            onPress: decreaseFontSize,
            backgroundColor: "#ffffff",
            color: "#1F2937", // Default primary text color
            autoClose: false, // Don't close Speed Dial for font size adjustments
        },
        {
            icon: "add",
            label: "Font Büyüt",
            onPress: increaseFontSize,
            backgroundColor: "#ffffff",
            color: "#1F2937", // Default primary text color
            autoClose: false, // Don't close Speed Dial for font size adjustments
        },
        {
            icon: "color-palette",
            label: "Renk Seç",
            onPress: () => setIsColorPickerVisible(true),
            backgroundColor: "#ffffff",
            color: "#8b5cf6",
            // autoClose defaults to true for color picker
        },
    ];

    return (
        <>
            <SafeAreaView style={{ ...styles.container, backgroundColor }}>
                <StatusBar barStyle="dark-content" backgroundColor={backgroundColor} />

                {/* Fixed Header with Back button */}
                <View style={[styles.header, { backgroundColor }]}>
                    <HeaderButton icon={<Ionicons name="chevron-back" size={20} color="black" />} onPress={handleGoBack} />

                    <View style={styles.headerCenter}>
                        <Typography variant="h4" align="center">
                            {bookTitle}
                        </Typography>
                        <Typography variant="caption" color="secondary" align="center">
                            Sayfa {currentPage} / {bookTotalPage}
                        </Typography>
                    </View>

                    {/* Empty space for symmetry */}
                    <View style={{ width: 80 }} />
                </View>

                {/* Book Page Container */}
                {/* <View style={[styles.bookContainer, { backgroundColor }]}> */}
                {/* Book Content */}
                <ScrollView
                    style={styles.contentContainer}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={styles.contentPadding}
                >
                    <InteractiveText
                        text={bookContent}
                        fontSize={fontSize}
                        lineHeight={fontSize * 1.5}
                        color={textColor}
                        style={styles.bookText}
                        onLoad={() => setBookLoading(false)}
                    />
                </ScrollView>
                {/* </View> */}

                {/* Speed Dial for Reading Controls */}
                <SpeedDial
                    actions={speedDialActions}
                    mainIcon="settings"
                    mainIconColor="#ffffff"
                    mainBackgroundColor="#3b82f6"
                    position="bottom-right"
                    size="medium"
                />

                {/* Color Picker Modal */}
                <ColorPicker
                    visible={isColorPickerVisible}
                    onClose={() => setIsColorPickerVisible(false)}
                    colors={backgroundColors}
                    selectedColor={backgroundColor}
                    onColorSelect={(colorOption) => {
                        setBackgroundColor(colorOption.color || "#fff");
                        setTextColor(colorOption.textColor || "#374151");
                    }}
                    title="Sayfa Rengi Seçin"
                />

                {/* Full-screen loading overlay */}
                <Loading
                    visible={bookLoading}
                    text="İçerik hazırlanıyor..."
                    color="#3b82f6"
                />
            </SafeAreaView>
            {/* Fixed Bottom Navigation */}
            {/* <View style={[styles.fixedBottomNavigation, { backgroundColor }]}>
                <NavigationButton
                    title="Önceki"
                    direction="prev"
                    variant={currentPage === 1 ? "disabled" : "primary"}
                    onPress={() => handlePageTurn("prev")}
                    disabled={currentPage === 1}
                />

                <View style={styles.pageIndicator}>
                    <Typography variant="body1" weight="bold">
                        {currentPage}
                    </Typography>
                </View>

                <NavigationButton
                    title="Sonraki"
                    direction="next"
                    variant={currentPage === oneBook.pages ? "disabled" : "primary"}
                    onPress={() => handlePageTurn("next")}
                    disabled={currentPage === oneBook.pages}
                />
            </View> */}
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff",
    },
    header: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderBottomWidth: 0,
    },

    headerCenter: {
        flex: 1,
        alignItems: "center",
        marginHorizontal: 16,
    },

    bookContainer: {
        flex: 1,
    },

    contentContainer: {
        flex: 1,
    },
    contentPadding: {
        padding: 24,
    },
    bookText: {
        fontFamily: "System",
        textAlign: "justify",
    },
    fixedBottomNavigation: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        padding: 20,
        paddingBottom: 40,
        borderTopWidth: 1,
        borderTopColor: "#e9ecef",
    },

    pageIndicator: {
        backgroundColor: "#f8f9fa",
        borderRadius: 20,
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: "#dee2e6",
    },
    pageNumber: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#495057",
    },
    editOverlay: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        zIndex: 2000,
    },
    editScrollView: {
        flex: 1,
        paddingTop: 60,
        paddingBottom: 20,
    },
    editPanel: {
        backgroundColor: "#ffffff",
        borderRadius: 16,
        padding: 24,
        margin: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 15,
    },
    editTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#212529",
        marginBottom: 24,
        textAlign: "center",
    },
    settingSection: {
        marginBottom: 24,
    },
    settingTitle: {
        fontSize: 16,
        fontWeight: "600",
        color: "#495057",
        marginBottom: 12,
    },
    fontSizeContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        flexWrap: "wrap",
    },
    fontSizeButton: {
        backgroundColor: "#f8f9fa",
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginBottom: 8,
        borderWidth: 1,
        borderColor: "#dee2e6",
        minWidth: 50,
        alignItems: "center",
    },
    activeFontSizeButton: {
        backgroundColor: "#667eea",
        borderColor: "#667eea",
    },
    fontSizeButtonText: {
        fontSize: 14,
        color: "#495057",
        fontWeight: "500",
    },
    activeFontSizeButtonText: {
        color: "#ffffff",
    },
    colorContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
    },
    colorButton: {
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 8,
        marginBottom: 8,
        borderWidth: 2,
        borderColor: "transparent",
        minWidth: 80,
        alignItems: "center",
    },
    activeColorButton: {
        borderColor: "#667eea",
        borderWidth: 2,
    },
    colorButtonText: {
        fontSize: 12,
        color: "#495057",
        fontWeight: "500",
    },
    lineHeightContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        flexWrap: "wrap",
    },
    lineHeightButton: {
        backgroundColor: "#f8f9fa",
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginBottom: 8,
        borderWidth: 1,
        borderColor: "#dee2e6",
        minWidth: 60,
        alignItems: "center",
    },
    activeLineHeightButton: {
        backgroundColor: "#667eea",
        borderColor: "#667eea",
    },
    lineHeightButtonText: {
        fontSize: 14,
        color: "#495057",
        fontWeight: "500",
    },
    activeLineHeightButtonText: {
        color: "#ffffff",
    },
    editOption: {
        backgroundColor: "#f8f9fa",
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: "#dee2e6",
    },
    editOptionText: {
        fontSize: 16,
        color: "#495057",
        fontWeight: "500",
    },
    closeButton: {
        backgroundColor: "#dc3545",
        borderRadius: 12,
        padding: 16,
        marginTop: 16,
        alignItems: "center",
    },
    closeButtonText: {
        fontSize: 16,
        color: "#ffffff",
        fontWeight: "600",
    },
});

export default ReadingViewScreen;
