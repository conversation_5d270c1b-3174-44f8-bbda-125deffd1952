import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { MainLayout } from "@layout";

const QuizScreen: React.FC = () => {
    const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
    const [currentQuestion, setCurrentQuestion] = useState(0);

    const sampleQuiz = {
        question: "What does 'serendipity' mean?",
        options: ["Tesadüf, şans eseri kar<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>, keder", "<PERSON><PERSON><PERSON><PERSON><PERSON> hareket", "<PERSON>üksek ses"],
        correctAnswer: 0,
    };

    return (
        <MainLayout backgroundColor="#f8fafc">
            <View>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Quiz Merkezi 🧠</Text>
                        <Text style={styles.subtitle}>
                            Bilginizi test edin ve kelime da<PERSON><PERSON><PERSON>ınızı geliştirin
                        </Text>
                    </View>

                    {/* Quiz Stats */}
                    <LinearGradient
                        colors={["#10b981", "#059669"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Quiz İstatistikleri</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>15</Text>
                                <Text style={styles.statLabel}>Tamamlanan</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>87%</Text>
                                <Text style={styles.statLabel}>Başarı Oranı</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>5</Text>
                                <Text style={styles.statLabel}>Günlük Hedef</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Current Quiz */}
                    <View style={styles.quizCard}>
                        <View style={styles.quizHeader}>
                            <View style={styles.progressContainer}>
                                <Text style={styles.progressText}>Soru 1/10</Text>
                                <View style={styles.progressBar}>
                                    <View style={[styles.progressFill, { width: "10%" }]} />
                                </View>
                            </View>
                            <View style={styles.categoryBadge}>
                                <Text style={styles.categoryText}>Kelime Anlamı</Text>
                            </View>
                        </View>

                        <Text style={styles.questionText}>{sampleQuiz.question}</Text>

                        <View style={styles.optionsContainer}>
                            {sampleQuiz.options.map((option, index) => (
                                <TouchableOpacity
                                    key={index}
                                    style={[
                                        styles.optionButton,
                                        selectedAnswer === index
                                            ? styles.selectedOption
                                            : styles.unselectedOption,
                                    ]}
                                    onPress={() => setSelectedAnswer(index)}
                                >
                                    <View style={styles.optionContent}>
                                        <View
                                            style={[
                                                styles.optionLetter,
                                                selectedAnswer === index
                                                    ? styles.selectedLetter
                                                    : styles.unselectedLetter,
                                            ]}
                                        >
                                            <Text
                                                style={[
                                                    styles.optionLetterText,
                                                    selectedAnswer === index
                                                        ? styles.selectedLetterText
                                                        : styles.unselectedLetterText,
                                                ]}
                                            >
                                                {String.fromCharCode(65 + index)}
                                            </Text>
                                        </View>
                                        <Text
                                            style={[
                                                styles.optionText,
                                                selectedAnswer === index
                                                    ? styles.selectedOptionText
                                                    : styles.unselectedOptionText,
                                            ]}
                                        >
                                            {option}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            ))}
                        </View>

                        <TouchableOpacity
                            style={[
                                styles.answerButton,
                                selectedAnswer !== null
                                    ? styles.enabledButton
                                    : styles.disabledButton,
                            ]}
                            disabled={selectedAnswer === null}
                        >
                            <Text style={styles.answerButtonText}>✓ Cevapla</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Quick Quiz Options */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>🚀 Hızlı Quiz Seçenekleri</Text>

                        <View style={styles.quizOptionsContainer}>
                            <LinearGradient
                                colors={["#4facfe", "#00f2fe"]}
                                style={styles.quizOptionCard}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                            >
                                <TouchableOpacity style={styles.quizOptionTouchable}>
                                    <View style={styles.quizOptionContent}>
                                        <View style={styles.quizOptionInfo}>
                                            <Text style={styles.quizOptionTitle}>
                                                Kelime Anlamları
                                            </Text>
                                            <Text style={styles.quizOptionSubtitle}>
                                                10 soruluk hızlı quiz
                                            </Text>
                                        </View>
                                        <View style={styles.quizOptionIcon}>
                                            <Text style={styles.quizOptionEmoji}>📚</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </LinearGradient>

                            <LinearGradient
                                colors={["#43e97b", "#38f9d7"]}
                                style={styles.quizOptionCard}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                            >
                                <TouchableOpacity style={styles.quizOptionTouchable}>
                                    <View style={styles.quizOptionContent}>
                                        <View style={styles.quizOptionInfo}>
                                            <Text style={styles.quizOptionTitle}>
                                                Cümle Tamamlama
                                            </Text>
                                            <Text style={styles.quizOptionSubtitle}>
                                                Bağlamda kelime kullanımı
                                            </Text>
                                        </View>
                                        <View style={styles.quizOptionIcon}>
                                            <Text style={styles.quizOptionEmoji}>✏️</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </LinearGradient>

                            <LinearGradient
                                colors={["#fa709a", "#fee140"]}
                                style={styles.quizOptionCard}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                            >
                                <TouchableOpacity style={styles.quizOptionTouchable}>
                                    <View style={styles.quizOptionContent}>
                                        <View style={styles.quizOptionInfo}>
                                            <Text style={styles.quizOptionTitle}>Karışık Quiz</Text>
                                            <Text style={styles.quizOptionSubtitle}>
                                                Tüm kelimelerden rastgele
                                            </Text>
                                        </View>
                                        <View style={styles.quizOptionIcon}>
                                            <Text style={styles.quizOptionEmoji}>🎲</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </LinearGradient>
                        </View>
                    </View>
            </View>
        </MainLayout>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120,
    },
    header: {
        marginBottom: 32,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    statsCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    statsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        textAlign: "center",
        marginBottom: 24,
    },
    statsContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    statItem: {
        alignItems: "center",
    },
    statNumber: {
        fontSize: 28,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 14,
        color: "rgba(255, 255, 255, 0.8)",
        fontWeight: "500",
        textAlign: "center",
    },
    quizCard: {
        backgroundColor: "white",
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 5,
    },
    quizHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 24,
    },
    progressContainer: {
        flex: 1,
        marginRight: 16,
    },
    progressText: {
        fontSize: 14,
        color: "#6B7280",
        fontWeight: "500",
        marginBottom: 8,
    },
    progressBar: {
        height: 6,
        backgroundColor: "#E5E7EB",
        borderRadius: 3,
    },
    progressFill: {
        height: "100%",
        backgroundColor: "#3B82F6",
        borderRadius: 3,
    },
    categoryBadge: {
        backgroundColor: "#DBEAFE",
        borderRadius: 12,
        paddingHorizontal: 12,
        paddingVertical: 6,
    },
    categoryText: {
        fontSize: 12,
        color: "#1E40AF",
        fontWeight: "600",
    },
    questionText: {
        fontSize: 20,
        fontWeight: "600",
        color: "#1F2937",
        marginBottom: 24,
        lineHeight: 28,
    },
    optionsContainer: {
        gap: 12,
        marginBottom: 24,
    },
    optionButton: {
        borderRadius: 12,
        padding: 16,
        borderWidth: 2,
    },
    selectedOption: {
        backgroundColor: "#EBF8FF",
        borderColor: "#3B82F6",
    },
    unselectedOption: {
        backgroundColor: "#F9FAFB",
        borderColor: "#E5E7EB",
    },
    optionContent: {
        flexDirection: "row",
        alignItems: "center",
    },
    optionLetter: {
        width: 32,
        height: 32,
        borderRadius: 16,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 12,
    },
    selectedLetter: {
        backgroundColor: "#3B82F6",
    },
    unselectedLetter: {
        backgroundColor: "#E5E7EB",
    },
    optionLetterText: {
        fontSize: 14,
        fontWeight: "bold",
    },
    selectedLetterText: {
        color: "white",
    },
    unselectedLetterText: {
        color: "#6B7280",
    },
    optionText: {
        fontSize: 16,
        flex: 1,
    },
    selectedOptionText: {
        color: "#1E40AF",
        fontWeight: "500",
    },
    unselectedOptionText: {
        color: "#374151",
    },
    answerButton: {
        borderRadius: 12,
        paddingVertical: 16,
        alignItems: "center",
    },
    enabledButton: {
        backgroundColor: "#3B82F6",
    },
    disabledButton: {
        backgroundColor: "#D1D5DB",
    },
    answerButtonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "bold",
    },
    section: {
        marginBottom: 32,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    quizOptionsContainer: {
        gap: 16,
    },
    quizOptionCard: {
        borderRadius: 16,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 12,
        elevation: 6,
    },
    quizOptionTouchable: {
        padding: 20,
    },
    quizOptionContent: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
    },
    quizOptionInfo: {
        flex: 1,
    },
    quizOptionTitle: {
        fontSize: 18,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    quizOptionSubtitle: {
        fontSize: 14,
        color: "rgba(255, 255, 255, 0.8)",
    },
    quizOptionIcon: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 25,
        padding: 12,
        marginLeft: 16,
    },
    quizOptionEmoji: {
        fontSize: 24,
    },
});

export default QuizScreen;
