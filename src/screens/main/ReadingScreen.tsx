import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

import { StackNavigationProp } from "@react-navigation/stack";
import { ReadingStackParamList } from "../../types/navigation";
import * as Books from "@public/books";
import { PDFUpload } from "../../components/ui";
import UserBooks from "../../components/reading/UserBooks";
import { UserBook } from "@services/firestore";
import { MainLayout } from "@layout";

type LanguageLevel = "A1" | "A2" | "B1" | "B2" | "C1" | "C2" | "CUSTOM";



type ReadingScreenNavigationProp = StackNavigationProp<ReadingStackParamList, "ReadingHome">;

interface ReadingScreenProps {
    navigation: ReadingScreenNavigationProp;
}

const ReadingScreen: React.FC<ReadingScreenProps> = ({ navigation }) => {
    const [selectedLevel, setSelectedLevel] = useState<LanguageLevel>("A1");
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [refreshKey, setRefreshKey] = useState(0);

    const handleBookSelect = (book: UserBook) => {
        navigation.navigate("ReadingView", {
            bookTitle: book.title,
            bookId: book.id || '',
            bookContent: book.content,
            bookTotalPage: book.pages,
            level: book.difficulty,
        });
    };

    const handleUploadComplete = (book: UserBook) => {
        Alert.alert(
            'Başarılı!',
            `"${book.title}" kitabı başarıyla yüklendi.`,
            [
                {
                    text: 'Şimdi Oku',
                    onPress: () => handleBookSelect(book)
                },
                {
                    text: 'Tamam',
                    style: 'default'
                }
            ]
        );
        setRefreshKey(prev => prev + 1);
    };

    const handleUploadError = (error: string) => {
        Alert.alert('Yükleme Hatası', error);
    };

    const levels = [
        {
            code: "A1" as LanguageLevel,
            name: "A1 - Başlangıç",
            description: "Temel kelimeler ve basit cümleler",
        },
        {
            code: "A2" as LanguageLevel,
            name: "A2 - Temel",
            description: "Günlük konuşmalar ve basit metinler",
        },
        // {
        //     code: "B1" as LanguageLevel,
        //     name: "B1 - Orta Alt",
        //     description: "Genel konular ve açık metinler",
        // },
        // {
        //     code: "B2" as LanguageLevel,
        //     name: "B2 - Orta Üst",
        //     description: "Karmaşık metinler ve soyut konular",
        // },
        // {
        //     code: "C1" as LanguageLevel,
        //     name: "C1 - İleri",
        //     description: "Uzun ve karmaşık metinler",
        // },
        // {
        //     code: "C2" as LanguageLevel,
        //     name: "C2 - Uzman",
        //     description: "Akademik ve profesyonel metinler",
        // },
        {
            code: "CUSTOM" as LanguageLevel,
            name: "📁 Kendi Kitap'larım",
            description: "Kendi yüklediğiniz PDF dosyaları",
        },
    ];

    const getSelectedLevelName = () => {
        if (!selectedLevel) return "Seviye seçin...";
        const level = levels.find((l) => l.code === selectedLevel);
        return level ? level.name : "Seviye seçin...";
    };

    const renderLevelDropdown = () => (
        <View style={[styles.dropdownSection, { zIndex: isDropdownOpen ? 1000 : 1 }]}>
            <Text style={styles.sectionTitle}>🎯 Seviye Seçin</Text>
            <View style={styles.dropdownContainer}>
                <TouchableOpacity
                    style={styles.dropdownButton}
                    onPress={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                    <Text style={styles.dropdownButtonText}>{getSelectedLevelName()}</Text>
                    <Text style={styles.dropdownArrow}>{isDropdownOpen ? "▲" : "▼"}</Text>
                </TouchableOpacity>

                {/* Native dropdown list positioned below the button */}
                {isDropdownOpen && (
                    <>
                        {/* Backdrop for tap-outside-to-close functionality */}
                        <TouchableOpacity
                            style={styles.dropdownBackdrop}
                            activeOpacity={1}
                            onPress={() => setIsDropdownOpen(false)}
                        />

                        {/* Dropdown list with scroll */}
                        <View style={styles.dropdownList}>
                            <ScrollView
                                style={styles.dropdownScrollView}
                                nestedScrollEnabled={true}
                            >
                                {levels.map((level, index) => (
                                    <TouchableOpacity
                                        key={level.code}
                                        style={[
                                            styles.dropdownItem,
                                            selectedLevel === level.code &&
                                                styles.selectedDropdownItem,
                                            index === 0 && styles.firstDropdownItem,
                                            index === levels.length - 1 && styles.lastDropdownItem,
                                        ]}
                                        onPress={() => {
                                            setSelectedLevel(level.code);
                                            setIsDropdownOpen(false);
                                        }}
                                    >
                                        <Text
                                            style={[
                                                styles.dropdownItemText,
                                                selectedLevel === level.code &&
                                                    styles.selectedDropdownItemText,
                                            ]}
                                        >
                                            {level.name}
                                        </Text>
                                        <Text style={styles.dropdownItemDescription}>
                                            {level.description}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </ScrollView>
                        </View>
                    </>
                )}
            </View>
        </View>
    );

    const renderCustomPDFSection = () => (
        <View style={styles.section}>
            <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { marginBottom: 0 }]}>📁 Kendi Kitap'larınız</Text>
                <PDFUpload
                    onUploadComplete={handleUploadComplete}
                    onUploadError={handleUploadError}
                />
            </View>

            {/* User's uploaded books */}
            <UserBooks
                key={refreshKey}
                onBookSelect={handleBookSelect}
                onRefresh={() => setRefreshKey(prev => prev + 1)}
            />
        </View>
    );

    const renderLevelBooks = () => {
        const levelBooks = Books[selectedLevel];

        return (
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>📚 {selectedLevel} Seviyesi Kitaplar</Text>
                <View style={styles.documentsContainer}>
                    {levelBooks?.map((book) => (
                        <View key={book.id} style={styles.documentCard}>
                            <View style={styles.documentHeader}>
                                <View style={styles.documentIcon}>
                                    <Text style={styles.documentEmoji}>📖</Text>
                                </View>
                                <View style={styles.documentInfo}>
                                    <Text style={styles.documentTitle}>{book.title}</Text>
                                    <Text style={styles.documentSubtitle}>
                                        {book.pages || 0} sayfa • ⭐ {book.rating || 0}
                                    </Text>
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.startButton}
                                onPress={() =>
                                    navigation.navigate("ReadingView", {
                                        bookTitle: book.title,
                                        bookId: book.id,
                                        bookContent: book.content,
                                        bookTotalPage: book.pages,
                                        level: selectedLevel || undefined,
                                    })
                                }
                            >
                                <Text style={styles.startButtonText}>Okumaya Başla</Text>
                            </TouchableOpacity>
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    const renderLevelContent = () => {
        if (selectedLevel === "CUSTOM") {
            return renderCustomPDFSection();
        } else {
            return renderLevelBooks();
        }
    };

    return (
        <MainLayout backgroundColor="#f8fafc">{/* Content wrapper for proper spacing */}
            <View>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Okuma Merkezi 📖</Text>
                        <Text style={styles.subtitle}>Seviyenizi seçin ve okumaya başlayın</Text>
                    </View>

                    {/* Level Dropdown */}
                    {renderLevelDropdown()}

                    {/* Content based on selected level */}
                    {selectedLevel && renderLevelContent()}

                    {/* Reading Tips */}
                    <LinearGradient
                        colors={["#a8edea", "#fed6e3"]}
                        style={styles.tipsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.tipsTitle}>💡 Okuma İpuçları</Text>
                        <View style={styles.tipsContainer}>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>👆</Text>
                                <Text style={styles.tipText}>
                                    Bilmediğiniz kelimelere dokunarak anlamlarını öğrenin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📝</Text>
                                <Text style={styles.tipText}>
                                    Önemli kelimeleri kelime listenize ekleyin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📅</Text>
                                <Text style={styles.tipText}>Düzenli okuma alışkanlığı edinin</Text>
                            </View>
                        </View>
                    </LinearGradient>
            </View>
        </MainLayout>
    );
};

const styles = StyleSheet.create({
    header: {
        marginBottom: 32,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    uploadCard: {
        borderRadius: 20,
        padding: 24,
        marginTop: 16,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    uploadContent: {
        alignItems: "center"
    },
    uploadIcon: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 50,
        padding: 16,
        marginBottom: 16,
    },
    uploadEmoji: {
        fontSize: 32,
    },
    uploadTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
        textAlign: "center",
    },
    uploadSubtitle: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
        marginBottom: 20,
    },
    uploadButton: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 12,
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderWidth: 1,
        borderColor: "rgba(255, 255, 255, 0.3)",
    },
    uploadButtonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "600",
    },
    section: {
        marginBottom: 32,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    documentsContainer: {
        gap: 16,
    },
    documentCard: {
        backgroundColor: "white",
        borderRadius: 16,
        padding: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    documentHeader: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 16,
    },
    documentIcon: {
        backgroundColor: "#F3F4F6",
        borderRadius: 12,
        padding: 12,
        marginRight: 12,
    },
    documentEmoji: {
        fontSize: 20,
    },
    documentInfo: {
        flex: 1,
    },
    documentTitle: {
        fontSize: 16,
        fontWeight: "600",
        color: "#1F2937",
        marginBottom: 4,
    },
    documentSubtitle: {
        fontSize: 14,
        color: "#6B7280",
    },
    progressContainer: {
        marginBottom: 16,
    },
    progressBar: {
        height: 6,
        backgroundColor: "#E5E7EB",
        borderRadius: 3,
        marginBottom: 8,
    },
    progressFill: {
        height: "100%",
        backgroundColor: "#3B82F6",
        borderRadius: 3,
    },
    progressText: {
        fontSize: 12,
        color: "#6B7280",
        fontWeight: "500",
    },
    continueButton: {
        backgroundColor: "#3B82F6",
        borderRadius: 8,
        paddingVertical: 8,
        paddingHorizontal: 16,
        alignSelf: "flex-start",
    },
    continueButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
    },
    tipsCard: {
        borderRadius: 20,
        padding: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    tipsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
        textAlign: "center",
    },
    tipsContainer: {
        gap: 12,
    },
    tipItem: {
        flexDirection: "row",
        alignItems: "center",
    },
    tipIcon: {
        fontSize: 20,
        marginRight: 12,
    },
    tipText: {
        fontSize: 16,
        color: "#374151",
        flex: 1,
    },
    // Dropdown styles
    dropdownSection: {
        marginBottom: 24,
    },
    dropdownContainer: {
        position: "relative",
        zIndex: 1000,
    },
    dropdownButton: {
        backgroundColor: "white",
        borderRadius: 12,
        padding: 16,
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#e5e7eb",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    dropdownButtonText: {
        fontSize: 16,
        fontWeight: "600",
        color: "#374151",
        flex: 1,
    },
    dropdownArrow: {
        fontSize: 14,
        color: "#6b7280",
        marginLeft: 8,
    },
    // Native dropdown styles
    dropdownBackdrop: {
        position: "absolute",
        top: 0,
        left: -1000,
        right: -1000,
        bottom: -1000,
        zIndex: 999,
    },
    dropdownList: {
        position: "absolute",
        top: "100%",
        left: 0,
        right: 0,
        backgroundColor: "white",
        borderRadius: 12,
        borderWidth: 1,
        borderColor: "#e5e7eb",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 10,
        maxHeight: 300,
        marginTop: 4,
        zIndex: 1001,
        overflow: "hidden",
    },
    dropdownScrollView: {
        maxHeight: 300,
    },
    dropdownItem: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#f3f4f6",
    },
    firstDropdownItem: {
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
    },
    lastDropdownItem: {
        borderBottomLeftRadius: 12,
        borderBottomRightRadius: 12,
        borderBottomWidth: 0,
    },
    selectedDropdownItem: {
        backgroundColor: "#f0f9ff",
    },
    dropdownItemText: {
        fontSize: 16,
        fontWeight: "600",
        color: "#374151",
        marginBottom: 4,
    },
    selectedDropdownItemText: {
        color: "#2563eb",
    },
    dropdownItemDescription: {
        fontSize: 14,
        color: "#6b7280",
        lineHeight: 18,
    },
    // Start button for level books
    startButton: {
        backgroundColor: "#10b981",
        borderRadius: 8,
        paddingVertical: 8,
        paddingHorizontal: 16,
        alignSelf: "flex-start",
    },
    startButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
    },
});

export default ReadingScreen;
