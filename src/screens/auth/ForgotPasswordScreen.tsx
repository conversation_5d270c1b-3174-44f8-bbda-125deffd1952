import React, { useState } from "react";
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { sendPasswordResetEmail } from "firebase/auth";
import { auth } from "@services/firebase";
import { useNavigation } from "@react-navigation/native";

const ForgotPasswordScreen: React.FC = () => {
    const [email, setEmail] = useState("");
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<{ email?: string }>({});
    const navigation = useNavigation();

    const validateForm = () => {
        const newErrors: { email?: string } = {};

        if (!email) {
            newErrors.email = "E-posta adresi gerekli";
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            newErrors.email = "Geç<PERSON><PERSON> bir e-posta adresi girin";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleResetPassword = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            await sendPasswordResetEmail(auth, email);
            Alert.alert("Başarılı", "Şifre sıfırlama bağlantısı e-posta adresinize gönderildi", [
                { text: "Tamam", onPress: () => navigation.goBack() },
            ]);
        } catch (error: any) {
            Alert.alert("Hata", error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <LinearGradient colors={["#667eea", "#764ba2"]} style={styles.container}>
            <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                style={styles.container}
            >
                <ScrollView
                    contentContainerStyle={styles.scrollContent}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.content}>
                        {/* Header */}
                        <View style={styles.header}>
                            <View style={styles.iconContainer}>
                                <Text style={styles.icon}>🔑</Text>
                            </View>
                            <Text style={styles.title}>Şifremi Unuttum</Text>
                            <Text style={styles.subtitle}>
                                E-posta adresinize şifre sıfırlama bağlantısı göndereceğiz
                            </Text>
                        </View>

                        {/* Reset Form */}
                        <View style={styles.formContainer}>
                            <Text style={styles.formTitle}>Şifre Sıfırlama</Text>

                            <View style={styles.inputContainer}>
                                <Text style={styles.label}>E-posta</Text>
                                <TextInput
                                    style={[styles.input, errors.email && styles.inputError]}
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChangeText={setEmail}
                                    keyboardType="email-address"
                                    autoCapitalize="none"
                                    placeholderTextColor="#9CA3AF"
                                />
                                {errors.email && (
                                    <Text style={styles.errorText}>{errors.email}</Text>
                                )}
                            </View>

                            <TouchableOpacity
                                style={[styles.button, loading && styles.buttonDisabled]}
                                onPress={handleResetPassword}
                                disabled={loading}
                            >
                                <Text style={styles.buttonText}>
                                    {loading
                                        ? "Gönderiliyor..."
                                        : "Şifre Sıfırlama Bağlantısı Gönder"}
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                            >
                                <Text style={styles.backButtonText}>← Geri Dön</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContent: {
        flexGrow: 1,
    },
    content: {
        flex: 1,
        justifyContent: "center",
        paddingHorizontal: 24,
        paddingVertical: 32,
    },
    header: {
        alignItems: "center",
        marginBottom: 48,
    },
    iconContainer: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 50,
        padding: 24,
        marginBottom: 24,
    },
    icon: {
        fontSize: 60,
    },
    title: {
        fontSize: 36,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
        lineHeight: 24,
    },
    formContainer: {
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        borderRadius: 20,
        padding: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    formTitle: {
        fontSize: 24,
        fontWeight: "bold",
        color: "#1F2937",
        textAlign: "center",
        marginBottom: 24,
    },
    inputContainer: {
        marginBottom: 16,
    },
    label: {
        fontSize: 16,
        fontWeight: "600",
        color: "#374151",
        marginBottom: 8,
    },
    input: {
        borderWidth: 2,
        borderColor: "#E5E7EB",
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        fontSize: 16,
        backgroundColor: "white",
        color: "#1F2937",
    },
    inputError: {
        borderColor: "#EF4444",
    },
    errorText: {
        color: "#EF4444",
        fontSize: 14,
        marginTop: 4,
    },
    button: {
        backgroundColor: "#3B82F6",
        borderRadius: 12,
        paddingVertical: 16,
        marginTop: 8,
        shadowColor: "#3B82F6",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 5,
    },
    buttonDisabled: {
        opacity: 0.6,
    },
    buttonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "600",
        textAlign: "center",
    },
    backButton: {
        marginTop: 16,
        paddingVertical: 12,
    },
    backButtonText: {
        color: "#3B82F6",
        fontSize: 16,
        fontWeight: "500",
        textAlign: "center",
    },
});

export default ForgotPasswordScreen;
