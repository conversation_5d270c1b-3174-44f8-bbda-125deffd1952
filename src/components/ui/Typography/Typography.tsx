import React from 'react';
import { Text, StyleSheet, TextStyle } from 'react-native';

export interface TypographyProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body1' | 'body2' | 'caption' | 'label' | 'error';
  color?: 'primary' | 'secondary' | 'white' | 'gray' | 'error' | 'success' | 'warning';
  align?: 'left' | 'center' | 'right';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  style?: TextStyle;
}

const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body1',
  color = 'primary',
  align = 'left',
  weight = 'normal',
  style,
}) => {
  const getVariantStyle = () => {
    switch (variant) {
      case 'h1':
        return styles.h1;
      case 'h2':
        return styles.h2;
      case 'h3':
        return styles.h3;
      case 'h4':
        return styles.h4;
      case 'body2':
        return styles.body2;
      case 'caption':
        return styles.caption;
      case 'label':
        return styles.label;
      case 'error':
        return styles.error;
      default:
        return styles.body1;
    }
  };

  const getColorStyle = () => {
    switch (color) {
      case 'secondary':
        return { color: '#6B7280' };
      case 'white':
        return { color: '#FFFFFF' };
      case 'gray':
        return { color: '#9CA3AF' };
      case 'error':
        return { color: '#EF4444' };
      case 'success':
        return { color: '#10B981' };
      case 'warning':
        return { color: '#F59E0B' };
      default:
        return { color: '#1F2937' };
    }
  };

  const getAlignStyle = () => {
    switch (align) {
      case 'center':
        return { textAlign: 'center' as const };
      case 'right':
        return { textAlign: 'right' as const };
      default:
        return { textAlign: 'left' as const };
    }
  };

  const getWeightStyle = () => {
    switch (weight) {
      case 'medium':
        return { fontWeight: '500' as const };
      case 'semibold':
        return { fontWeight: '600' as const };
      case 'bold':
        return { fontWeight: 'bold' as const };
      default:
        return { fontWeight: 'normal' as const };
    }
  };

  const textStyle = [
    getVariantStyle(),
    getColorStyle(),
    getAlignStyle(),
    getWeightStyle(),
    style,
  ];

  return <Text style={textStyle}>{children}</Text>;
};

const styles = StyleSheet.create({
  h1: {
    fontSize: 32,
    lineHeight: 40,
    fontWeight: 'bold',
  },
  h2: {
    fontSize: 24,
    lineHeight: 32,
    fontWeight: 'bold',
  },
  h3: {
    fontSize: 20,
    lineHeight: 28,
    fontWeight: 'bold',
  },
  h4: {
    fontSize: 18,
    lineHeight: 24,
    fontWeight: '600',
  },
  body1: {
    fontSize: 16,
    lineHeight: 24,
  },
  body2: {
    fontSize: 14,
    lineHeight: 20,
  },
  caption: {
    fontSize: 12,
    lineHeight: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
  },
  error: {
    fontSize: 14,
    color: '#EF4444',
  },
});

export default Typography;
