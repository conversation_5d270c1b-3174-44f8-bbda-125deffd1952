import React from 'react';
import { View, StyleSheet, ViewStyle, ColorValue } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'gradient' | 'glass' | 'elevated';
  gradientColors?: readonly [ColorValue, ColorValue, ...ColorValue[]];
  style?: ViewStyle;
  padding?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'small' | 'medium' | 'large' | 'xl';
  shadow?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  gradientColors = ['#667eea', '#764ba2'],
  style,
  padding = 'medium',
  borderRadius = 'medium',
  shadow = true,
}) => {
  const getPaddingStyle = () => {
    switch (padding) {
      case 'none':
        return { padding: 0 };
      case 'small':
        return { padding: 12 };
      case 'large':
        return { padding: 24 };
      default:
        return { padding: 16 };
    }
  };

  const getBorderRadiusStyle = () => {
    switch (borderRadius) {
      case 'small':
        return { borderRadius: 8 };
      case 'large':
        return { borderRadius: 20 };
      case 'xl':
        return { borderRadius: 24 };
      default:
        return { borderRadius: 12 };
    }
  };

  const getBaseStyle = () => {
    const baseStyle = [
      getPaddingStyle(),
      getBorderRadiusStyle(),
      shadow && styles.shadow,
      style,
    ];

    switch (variant) {
      case 'gradient':
        return baseStyle;
      case 'glass':
        return [
          ...baseStyle,
          styles.glassCard,
        ];
      case 'elevated':
        return [
          ...baseStyle,
          styles.elevatedCard,
        ];
      default:
        return [
          ...baseStyle,
          styles.defaultCard,
        ];
    }
  };

  if (variant === 'gradient') {
    return (
      <LinearGradient
        colors={gradientColors}
        style={getBaseStyle()}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {children}
      </LinearGradient>
    );
  }

  return (
    <View style={getBaseStyle()}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  defaultCard: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  glassCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  elevatedCard: {
    backgroundColor: '#FFFFFF',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
});

export default Card;
