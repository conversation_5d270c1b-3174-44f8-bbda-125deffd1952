import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Modal } from 'react-native';

export interface LoadingProps {
  /**
   * Whether the loading overlay is visible
   */
  visible: boolean;
  /**
   * Loading text to display below the spinner
   */
  text?: string;
  /**
   * Size of the loading spinner
   */
  size?: 'small' | 'large';
  /**
   * Color of the loading spinner
   */
  color?: string;
  /**
   * Whether to show as a modal overlay (default) or inline component
   */
  overlay?: boolean;
  /**
   * Custom style for the loading container
   */
  containerStyle?: object;
  /**
   * Custom style for the loading content
   */
  contentStyle?: object;
}

const Loading: React.FC<LoadingProps> = ({
  visible,
  text = 'Yükleniyor...',
  size = 'large',
  color = '#3B82F6',
  overlay = true,
  containerStyle,
  contentStyle,
}) => {
  const loadingContent = (
    <View style={[styles.container, !overlay && styles.inlineContainer, containerStyle]}>
      <View style={[styles.content, contentStyle]}>
        <ActivityIndicator 
          size={size} 
          color={color} 
          style={styles.spinner}
        />
        {text && (
          <Text style={styles.text}>{text}</Text>
        )}
      </View>
    </View>
  );

  if (!visible) {
    return null;
  }

  if (overlay) {
    return (
      <Modal
        transparent
        visible={visible}
        animationType="fade"
        statusBarTranslucent
      >
        {loadingContent}
      </Modal>
    );
  }

  return loadingContent;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  inlineContainer: {
    backgroundColor: 'transparent',
    paddingVertical: 20,
  },
  content: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingVertical: 24,
    paddingHorizontal: 32,
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    minWidth: 120,
  },
  spinner: {
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default Loading;
