import React, { useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Alert,
    Modal,
    TextInput,
    ScrollView,
} from 'react-native';
import { Ionico<PERSON>, Feather, AntDesign } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { LinearGradient } from 'expo-linear-gradient';
import Loading from '../Loading';
import { pdfProcessorService, PDFUploadProgress } from '@services/pdfProcessor';
import { bookService, UserBook } from '@services/firestore';
import { authService } from '@services/auth';
import { Timestamp } from 'firebase/firestore';

export interface PDFUploadProps {
    onUploadComplete?: (book: UserBook) => void;
    onUploadError?: (error: string) => void;
}

const PDFUpload: React.FC<PDFUploadProps> = ({ onUploadComplete, onUploadError }) => {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState<PDFUploadProgress | null>(null);
    const [showMetadataModal, setShowMetadataModal] = useState(false);
    const [selectedFile, setSelectedFile] = useState<DocumentPicker.DocumentPickerAsset | null>(null);
    const [extractedContent, setExtractedContent] = useState<string>('');
    const [bookMetadata, setBookMetadata] = useState({
        title: '',
        difficulty: 'A2' as "A1" | "A2" | "B1" | "B2" | "C1" | "C2",
        pages: 0,
        wordCount: 0,
    });

    const difficultyLevels = [
        { value: 'A1', label: 'A1 - Başlangıç' },
        { value: 'A2', label: 'A2 - Temel' },
        { value: 'B1', label: 'B1 - Orta Alt' },
        { value: 'B2', label: 'B2 - Orta Üst' },
        { value: 'C1', label: 'C1 - İleri' },
        { value: 'C2', label: 'C2 - Uzman' },
    ];

    const handleFileSelection = async () => {
        try {
            setIsUploading(true);
            setUploadProgress({
                stage: 'selecting',
                progress: 0,
                message: 'Dosya seçiliyor...'
            });

            const result = await pdfProcessorService.pickPDFFile();
            
            if (!result || result.canceled) {
                setIsUploading(false);
                setUploadProgress(null);
                return;
            }

            const file = result.assets[0];
            setSelectedFile(file);

            // Process the PDF file
            const processingResult = await pdfProcessorService.processPDFFile(
                file,
                (progress) => setUploadProgress(progress)
            );

            if (processingResult.success && processingResult.content) {
                setExtractedContent(processingResult.content);
                
                // Auto-generate metadata
                const title = pdfProcessorService.generateTitleFromFilename(file.name);
                const difficulty = pdfProcessorService.detectDifficultyLevel(processingResult.content);
                
                setBookMetadata({
                    title,
                    difficulty,
                    pages: processingResult.pageCount || 1,
                    wordCount: processingResult.wordCount || 0,
                });

                setIsUploading(false);
                setUploadProgress(null);
                setShowMetadataModal(true);
            } else {
                throw new Error(processingResult.error || 'PDF işleme başarısız');
            }

        } catch (error) {
            console.error('PDF upload error:', error);
            setIsUploading(false);
            setUploadProgress(null);
            Alert.alert('Hata', 'PDF dosyası yüklenirken bir hata oluştu.');
            onUploadError?.(error instanceof Error ? error.message : 'Bilinmeyen hata');
        }
    };

    const handleSaveBook = async () => {
        try {
            const currentUser = authService.getCurrentUser();
            if (!currentUser) {
                Alert.alert('Hata', 'Kitap kaydetmek için giriş yapmanız gerekiyor.');
                return;
            }

            if (!selectedFile || !extractedContent) {
                Alert.alert('Hata', 'Geçerli bir dosya seçilmedi.');
                return;
            }

            setIsUploading(true);
            setUploadProgress({
                stage: 'uploading',
                progress: 90,
                message: 'Kitap kaydediliyor...'
            });

            const bookData: Omit<UserBook, 'id'> = {
                userId: currentUser.uid,
                title: bookMetadata.title,
                difficulty: bookMetadata.difficulty,
                pages: bookMetadata.pages,
                content: extractedContent,
                isUserUploaded: true,
                uploadDate: Timestamp.now(),
                originalFilename: selectedFile.name,
                processingStatus: 'completed',
                fileSize: selectedFile.size,
                wordCount: bookMetadata.wordCount,
                readingProgress: 0,
            };

            const bookId = await bookService.addUserBook(bookData);
            
            const savedBook: UserBook = {
                id: bookId,
                ...bookData,
            };

            setUploadProgress({
                stage: 'completed',
                progress: 100,
                message: 'Kitap başarıyla kaydedildi!'
            });

            setTimeout(() => {
                setIsUploading(false);
                setUploadProgress(null);
                setShowMetadataModal(false);
                resetState();
                onUploadComplete?.(savedBook);
            }, 1000);

        } catch (error) {
            console.error('Error saving book:', error);
            setIsUploading(false);
            setUploadProgress(null);
            Alert.alert('Hata', 'Kitap kaydedilirken bir hata oluştu.');
            onUploadError?.(error instanceof Error ? error.message : 'Bilinmeyen hata');
        }
    };

    const resetState = () => {
        setSelectedFile(null);
        setExtractedContent('');
        setBookMetadata({
            title: '',
            difficulty: 'A2',
            pages: 0,
            wordCount: 0,
        });
    };

    const handleCancel = () => {
        setShowMetadataModal(false);
        resetState();
    };

    return (
        <>
        {/* <LinearGradient
                colors={["#4facfe", "#00f2fe"]}
                style={styles.uploadCard}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
            >
                <View style={styles.uploadContent}>
                    <View style={styles.uploadIcon}>
                        <Text style={styles.uploadEmoji}>📄</Text>
                    </View>
                    <Text style={styles.uploadTitle}>
                        Yeni PDF Yükle
                    </Text>
                    <Text style={styles.uploadSubtitle}>
                        Okumak istediğiniz yeni PDF dosyasını seçin
                    </Text>
                    <TouchableOpacity style={styles.uploadButton} onPress={handleFileSelection}>
                        <Text style={styles.uploadButtonText}>📁 Dosya Seç</Text>
                    </TouchableOpacity>
                </View>
            </LinearGradient> */}
            <TouchableOpacity style={styles.uploadButton} onPress={handleFileSelection}>
                    <AntDesign name="pluscircleo" size={24} color="black" />
                    {/* <Feather name="upload" size={24} color="black" /> */}
                    {/* <Text style={styles.uploadButtonText}>PDF Yükle</Text> */}
            </TouchableOpacity>

            {/* Upload Progress Loading */}
            <Loading
                visible={isUploading}
                text={uploadProgress?.message || 'Yükleniyor...'}
            />

            {/* Metadata Edit Modal */}
            <Modal
                visible={showMetadataModal}
                animationType="slide"
                transparent={true}
                onRequestClose={handleCancel}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <ScrollView showsVerticalScrollIndicator={false}>
                            <Text style={styles.modalTitle}>Kitap Bilgileri</Text>
                            
                            <View style={styles.inputGroup}>
                                <Text style={styles.label}>Kitap Başlığı</Text>
                                <TextInput
                                    style={styles.input}
                                    value={bookMetadata.title}
                                    onChangeText={(text) => setBookMetadata(prev => ({ ...prev, title: text }))}
                                    placeholder="Kitap başlığını girin"
                                />
                            </View>

                            <View style={styles.inputGroup}>
                                <Text style={styles.label}>Zorluk Seviyesi</Text>
                                <View style={styles.difficultyContainer}>
                                    {difficultyLevels.map((level) => (
                                        <TouchableOpacity
                                            key={level.value}
                                            style={[
                                                styles.difficultyButton,
                                                bookMetadata.difficulty === level.value && styles.selectedDifficulty
                                            ]}
                                            onPress={() => setBookMetadata(prev => ({ 
                                                ...prev, 
                                                difficulty: level.value as any 
                                            }))}
                                        >
                                            <Text style={[
                                                styles.difficultyText,
                                                bookMetadata.difficulty === level.value && styles.selectedDifficultyText
                                            ]}>
                                                {level.label}
                                            </Text>
                                        </TouchableOpacity>
                                    ))}
                                </View>
                            </View>

                            <View style={styles.statsContainer}>
                                <View style={styles.statItem}>
                                    <Text style={styles.statLabel}>Sayfa Sayısı</Text>
                                    <Text style={styles.statValue}>{bookMetadata.pages || 0}</Text>
                                </View>
                                <View style={styles.statItem}>
                                    <Text style={styles.statLabel}>Kelime Sayısı</Text>
                                    <Text style={styles.statValue}>{bookMetadata.wordCount || 0}</Text>
                                </View>
                            </View>

                            <View style={styles.buttonContainer}>
                                <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                                    <Text style={styles.cancelButtonText}>İptal</Text>
                                </TouchableOpacity>
                                <TouchableOpacity style={styles.saveButton} onPress={handleSaveBook}>
                                    <Text style={styles.saveButtonText}>Kaydet</Text>
                                </TouchableOpacity>
                            </View>
                        </ScrollView>
                    </View>
                </View>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({
    uploadCard: {
        borderRadius: 20,
        padding: 24,
        marginTop: 10,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    uploadContent: {
        alignItems: "center"
    },
    uploadIcon: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 50,
        padding: 16,
        marginBottom: 16,
    },
    uploadEmoji: {
        fontSize: 32,
    },
    uploadTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
        textAlign: "center",
    },
    uploadSubtitle: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
        marginBottom: 20,
    },
    uploadButton: {
        // paddingHorizontal: 24,
        // paddingVertical: 12,
    },
    uploadButtonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "600",
    },
    gradient: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        paddingHorizontal: 20,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 24,
        width: '100%',
        maxHeight: '80%',
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 20,
        textAlign: 'center',
    },
    inputGroup: {
        marginBottom: 16,
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        color: '#374151',
        marginBottom: 8,
    },
    input: {
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        color: '#1f2937',
    },
    difficultyContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    difficultyButton: {
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#d1d5db',
        backgroundColor: '#f9fafb',
    },
    selectedDifficulty: {
        backgroundColor: '#3b82f6',
        borderColor: '#3b82f6',
    },
    difficultyText: {
        fontSize: 14,
        color: '#374151',
    },
    selectedDifficultyText: {
        color: 'white',
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginVertical: 16,
        padding: 16,
        backgroundColor: '#f3f4f6',
        borderRadius: 8,
    },
    statItem: {
        alignItems: 'center',
    },
    statLabel: {
        fontSize: 14,
        color: '#6b7280',
        marginBottom: 4,
    },
    statValue: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#1f2937',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        marginRight: 8,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#d1d5db',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#374151',
    },
    saveButton: {
        flex: 1,
        paddingVertical: 12,
        marginLeft: 8,
        borderRadius: 8,
        backgroundColor: '#3b82f6',
        alignItems: 'center',
    },
    saveButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '600',
    },
});

export default PDFUpload;
