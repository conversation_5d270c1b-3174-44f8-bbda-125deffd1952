import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';

export interface SettingsButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'default' | 'active' | 'color';
  active?: boolean;
  backgroundColor?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const SettingsButton: React.FC<SettingsButtonProps> = ({
  title,
  onPress,
  variant = 'default',
  active = false,
  backgroundColor,
  style,
  textStyle,
}) => {
  const getVariantStyles = () => {
    if (variant === 'color' && backgroundColor) {
      return {
        backgroundColor: backgroundColor,
        borderColor: active ? '#667eea' : 'transparent',
        textColor: '#495057',
      };
    }

    switch (variant) {
      case 'active':
        return {
          backgroundColor: '#667eea',
          borderColor: '#667eea',
          textColor: '#FFFFFF',
        };
      default:
        return {
          backgroundColor: active ? '#667eea' : '#f8f9fa',
          borderColor: active ? '#667eea' : '#dee2e6',
          textColor: active ? '#FFFFFF' : '#495057',
        };
    }
  };

  const variantStyles = getVariantStyles();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: variantStyles.backgroundColor,
      borderColor: variantStyles.borderColor,
    },
    variant === 'color' && active && styles.activeColorButton,
    style,
  ];

  const textStyles = [
    styles.buttonText,
    {
      color: variantStyles.textColor,
    },
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <Text style={textStyles}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    marginBottom: 8,
  },
  activeColorButton: {
    borderWidth: 2,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default SettingsButton;
