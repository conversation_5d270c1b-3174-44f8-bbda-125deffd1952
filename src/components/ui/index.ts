// Main UI Components
export { default as But<PERSON> } from "./Button";
export type { ButtonProps } from "./Button";

export { default as Input } from "./Input";
export type { InputProps } from "./Input";

export { default as Card } from "./Card";
export type { CardProps } from "./Card";

export { default as Typography } from "./Typography";
export type { TypographyProps } from "./Typography";

export { default as Loading } from "./Loading";
export type { LoadingProps } from "./Loading";

// Specialized Button Components
export { default as HeaderButton } from "./HeaderButton";
export type { HeaderButtonProps } from "./HeaderButton";

export { default as NavigationButton } from "./NavigationButton";
export type { NavigationButtonProps } from "./NavigationButton";

export { default as SettingsButton } from "./SettingsButton";
export type { SettingsButtonProps } from "./SettingsButton";

// New UI Components
export { default as SpeedDial } from "./SpeedDial";
export type { SpeedDialAction } from "./SpeedDial";

export { default as ColorPicker } from "./ColorPicker";
export type { ColorOption } from "./ColorPicker";

export { default as PDFUpload } from "./PDFUpload";
export type { PDFUploadProps } from "./PDFUpload";

// Legacy components (keeping for backward compatibility)
export { default as TabIcon } from "./TabIcon";
export { default as AddWordModal } from "./AddWordModal";
