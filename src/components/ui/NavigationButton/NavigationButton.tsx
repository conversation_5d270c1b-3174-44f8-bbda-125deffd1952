import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';

export interface NavigationButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'disabled';
  direction?: 'prev' | 'next';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const NavigationButton: React.FC<NavigationButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  direction,
  disabled = false,
  style,
  textStyle,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return {
          backgroundColor: '#6B7280',
          textColor: '#FFFFFF',
        };
      case 'disabled':
        return {
          backgroundColor: '#D1D5DB',
          textColor: '#9CA3AF',
        };
      default:
        return {
          backgroundColor: '#667eea',
          textColor: '#FFFFFF',
        };
    }
  };

  const getDirectionIcon = () => {
    if (direction === 'prev') return '←';
    if (direction === 'next') return '→';
    return '';
  };

  const variantStyles = getVariantStyles();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: variantStyles.backgroundColor,
      opacity: disabled ? 0.6 : 1,
    },
    style,
  ];

  const textStyles = [
    styles.buttonText,
    {
      color: variantStyles.textColor,
    },
    textStyle,
  ];

  const displayTitle = direction === 'prev' 
    ? `${getDirectionIcon()} ${title}`
    : direction === 'next'
    ? `${title} ${getDirectionIcon()}`
    : title;

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text style={textStyles}>{displayTitle}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default NavigationButton;
