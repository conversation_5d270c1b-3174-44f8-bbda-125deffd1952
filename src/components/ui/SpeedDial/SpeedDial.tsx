import React, { useState, useRef } from "react";
import { View, TouchableOpacity, Animated, StyleSheet, Dimensions } from "react-native";
import { Ionicons } from "@expo/vector-icons";

const { width, height } = Dimensions.get("window");

export interface SpeedDialAction {
    icon: keyof typeof Ionicons.glyphMap;
    label: string;
    onPress: () => void;
    color?: string;
    backgroundColor?: string;
    autoClose?: boolean; // Whether to close the Speed Dial after action
}

interface SpeedDialProps {
    actions: SpeedDialAction[];
    mainIcon?: keyof typeof Ionicons.glyphMap;
    mainIconColor?: string;
    mainBackgroundColor?: string;
    position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
    size?: "small" | "medium" | "large";
}

const SpeedDial: React.FC<SpeedDialProps> = ({
    actions,
    mainIcon = "settings",
    mainIconColor = "#ffffff",
    mainBackgroundColor = "#3b82f6",
    position = "bottom-right",
    size = "medium",
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const animatedValue = useRef(new Animated.Value(0)).current;
    const rotateValue = useRef(new Animated.Value(0)).current;

    const getSizeConfig = () => {
        switch (size) {
            case "small":
                return { main: 48, action: 40, spacing: 60 };
            case "large":
                return { main: 64, action: 52, spacing: 80 };
            default:
                return { main: 56, action: 44, spacing: 70 };
        }
    };

    const getPositionStyle = () => {
        const margin = 15;
        switch (position) {
            case "bottom-left":
                return { bottom: margin, left: margin };
            case "top-right":
                return { top: margin + 60, right: margin }; // +60 for header space
            case "top-left":
                return { top: margin + 60, left: margin };
            default:
                return { bottom: margin + 80, right: margin }; // +80 for bottom navigation
        }
    };

    const sizeConfig = getSizeConfig();

    const toggleSpeedDial = () => {
        const toValue = isOpen ? 0 : 1;

        Animated.parallel([
            Animated.spring(animatedValue, {
                toValue,
                useNativeDriver: true,
                tension: 100,
                friction: 8,
            }),
            Animated.timing(rotateValue, {
                toValue,
                duration: 300,
                useNativeDriver: true,
            }),
        ]).start();

        setIsOpen(!isOpen);
    };

    const renderActionButton = (action: SpeedDialAction, index: number) => {
        const translateY = animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0, -(sizeConfig.spacing * (index + 1))],
        });

        const scale = animatedValue.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0, 0.8, 1],
        });

        const opacity = animatedValue.interpolate({
            inputRange: [0, 0.3, 1],
            outputRange: [0, 0.5, 1],
        });

        return (
            <Animated.View
                key={index}
                style={[
                    styles.actionButton,
                    {
                        width: sizeConfig.action,
                        height: sizeConfig.action,
                        borderRadius: sizeConfig.action / 2,
                        backgroundColor: action.backgroundColor || "#ffffff",
                        transform: [{ translateY }, { scale }],
                        opacity,
                    },
                ]}
            >
                <TouchableOpacity
                    style={[
                        styles.actionButtonTouchable,
                        {
                            width: sizeConfig.action,
                            height: sizeConfig.action,
                            borderRadius: sizeConfig.action / 2,
                        },
                    ]}
                    onPress={() => {
                        action.onPress();
                        // Only close if autoClose is not explicitly set to false
                        if (action.autoClose !== false) {
                            toggleSpeedDial();
                        }
                    }}
                    activeOpacity={0.8}
                >
                    <Ionicons
                        name={action.icon}
                        size={sizeConfig.action * 0.5}
                        color={action.color || "#3b82f6"}
                    />
                </TouchableOpacity>
            </Animated.View>
        );
    };

    const mainButtonRotation = rotateValue.interpolate({
        inputRange: [0, 1],
        outputRange: ["0deg", "45deg"],
    });

    return (
        <View style={[styles.container, getPositionStyle()]}>
            {/* Action Buttons */}
            {actions.map((action, index) => renderActionButton(action, index))}

            {/* Main Button */}
            <Animated.View
                style={[
                    styles.mainButton,
                    {
                        width: sizeConfig.main,
                        height: sizeConfig.main,
                        borderRadius: sizeConfig.main / 2,
                        backgroundColor: mainBackgroundColor,
                        transform: [{ rotate: mainButtonRotation }],
                    },
                ]}
            >
                <TouchableOpacity
                    style={[
                        styles.mainButtonTouchable,
                        {
                            width: sizeConfig.main,
                            height: sizeConfig.main,
                            borderRadius: sizeConfig.main / 2,
                        },
                    ]}
                    onPress={toggleSpeedDial}
                    activeOpacity={0.8}
                >
                    <Ionicons
                        name={isOpen ? "close" : mainIcon}
                        size={sizeConfig.main * 0.5}
                        color={mainIconColor}
                    />
                </TouchableOpacity>
            </Animated.View>

            {/* Backdrop */}
            {isOpen && (
                <TouchableOpacity
                    style={styles.backdrop}
                    onPress={toggleSpeedDial}
                    activeOpacity={1}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        alignItems: "center",
        zIndex: 1000,
    },
    mainButton: {
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    mainButtonTouchable: {
        justifyContent: "center",
        alignItems: "center",
    },
    actionButton: {
        position: "absolute",
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 4,
        borderWidth: 1,
        borderColor: "#e5e7eb",
    },
    actionButtonTouchable: {
        justifyContent: "center",
        alignItems: "center",
    },
    backdrop: {
        position: "absolute",
        top: -height,
        left: -width,
        width: width * 2,
        height: height * 2,
        backgroundColor: "transparent",
        zIndex: -1,
    },
});

export default SpeedDial;
