import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  icon?: string;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  icon,
  fullWidth = false,
  style,
  textStyle,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: '#3B82F6',
          textColor: '#FFFFFF',
          borderColor: '#3B82F6',
          shadowColor: '#3B82F6',
        };
      case 'secondary':
        return {
          backgroundColor: '#667eea',
          textColor: '#FFFFFF',
          borderColor: '#667eea',
          shadowColor: '#667eea',
        };
      case 'success':
        return {
          backgroundColor: '#10b981',
          textColor: '#FFFFFF',
          borderColor: '#10b981',
          shadowColor: '#10b981',
        };
      case 'danger':
        return {
          backgroundColor: '#ef4444',
          textColor: '#FFFFFF',
          borderColor: '#ef4444',
          shadowColor: '#ef4444',
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          textColor: '#3B82F6',
          borderColor: '#3B82F6',
          shadowColor: '#000000',
        };
      case 'ghost':
        return {
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          textColor: '#FFFFFF',
          borderColor: 'rgba(255, 255, 255, 0.3)',
          shadowColor: 'transparent',
        };
      default:
        return {
          backgroundColor: '#3B82F6',
          textColor: '#FFFFFF',
          borderColor: '#3B82F6',
          shadowColor: '#3B82F6',
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 8,
          paddingHorizontal: 16,
          fontSize: 14,
          borderRadius: 8,
        };
      case 'large':
        return {
          paddingVertical: 16,
          paddingHorizontal: 32,
          fontSize: 18,
          borderRadius: 16,
        };
      default:
        return {
          paddingVertical: 12,
          paddingHorizontal: 24,
          fontSize: 16,
          borderRadius: 12,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const isDisabled = disabled || loading;

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: variantStyles.backgroundColor,
      borderColor: variantStyles.borderColor,
      paddingVertical: sizeStyles.paddingVertical,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      borderRadius: sizeStyles.borderRadius,
      width: fullWidth ? '100%' : undefined,
      opacity: isDisabled ? 0.6 : 1,
      shadowColor: variantStyles.shadowColor,
    },
    variant === 'outline' && styles.outlineButton,
    style,
  ];

  const textStyles = [
    styles.buttonText,
    {
      color: variantStyles.textColor,
      fontSize: sizeStyles.fontSize,
    },
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={0.8}
    >
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={variantStyles.textColor} 
          style={styles.loadingIndicator}
        />
      )}
      {icon && !loading && (
        <Text style={[textStyles, styles.icon]}>{icon}</Text>
      )}
      <Text style={textStyles}>
        {loading ? 'Yükleniyor...' : title}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
  },
  buttonText: {
    fontWeight: '600',
    textAlign: 'center',
  },
  loadingIndicator: {
    marginRight: 8,
  },
  icon: {
    marginRight: 8,
  },
});

export default Button;
