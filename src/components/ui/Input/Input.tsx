import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ViewStyle, TextStyle, TextInputProps } from 'react-native';

export interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  icon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  variant?: 'default' | 'floating';
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  icon,
  rightIcon,
  onRightIconPress,
  variant = 'default',
  value,
  onChangeText,
  placeholder,
  secureTextEntry,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecure, setIsSecure] = useState(secureTextEntry);

  const handleRightIconPress = () => {
    if (secureTextEntry) {
      setIsSecure(!isSecure);
    } else if (onRightIconPress) {
      onRightIconPress();
    }
  };

  const getRightIcon = () => {
    if (secureTextEntry) {
      return isSecure ? '👁️' : '🙈';
    }
    return rightIcon;
  };

  const getContainerStyle = () => {
    const baseStyle = [styles.container];
    if (containerStyle) baseStyle.push(containerStyle);
    return baseStyle;
  };

  const getInputContainerStyle = () => {
    const baseStyle = [
      styles.inputContainer,
      isFocused && styles.inputContainerFocused,
      error && styles.inputContainerError,
    ];
    return baseStyle;
  };

  const getInputStyle = () => {
    const baseStyle = [
      styles.input,
      icon && styles.inputWithLeftIcon,
      getRightIcon() && styles.inputWithRightIcon,
    ];
    if (inputStyle) baseStyle.push(inputStyle);
    return baseStyle;
  };

  if (variant === 'floating') {
    return (
      <View style={getContainerStyle()}>
        <View style={getInputContainerStyle()}>
          {icon && (
            <View style={styles.leftIconContainer}>
              <Text style={styles.icon}>{icon}</Text>
            </View>
          )}
          
          <TextInput
            value={value}
            onChangeText={onChangeText}
            placeholder={placeholder}
            secureTextEntry={isSecure}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            style={getInputStyle()}
            placeholderTextColor="#9CA3AF"
            {...props}
          />
          
          {label && (
            <Text style={[
              styles.floatingLabel,
              isFocused && styles.floatingLabelFocused,
              error && styles.floatingLabelError,
              (isFocused || value) && styles.floatingLabelActive,
              labelStyle,
            ]}>
              {label}
            </Text>
          )}
          
          {getRightIcon() && (
            <TouchableOpacity
              onPress={handleRightIconPress}
              style={styles.rightIconContainer}
            >
              <Text style={styles.icon}>{getRightIcon()}</Text>
            </TouchableOpacity>
          )}
        </View>
        
        {error && (
          <Text style={[styles.errorText, errorStyle]}>
            {error}
          </Text>
        )}
      </View>
    );
  }

  return (
    <View style={getContainerStyle()}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
        </Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {icon && (
          <View style={styles.leftIconContainer}>
            <Text style={[styles.icon, styles.leftIcon]}>{icon}</Text>
          </View>
        )}
        
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          secureTextEntry={isSecure}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          style={getInputStyle()}
          placeholderTextColor="#9CA3AF"
          {...props}
        />
        
        {getRightIcon() && (
          <TouchableOpacity
            onPress={handleRightIconPress}
            style={styles.rightIconContainer}
          >
            <Text style={styles.icon}>{getRightIcon()}</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  inputContainer: {
    position: 'relative',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputContainerFocused: {
    borderColor: '#3B82F6',
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 3,
  },
  inputContainerError: {
    borderColor: '#EF4444',
  },
  input: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  inputWithLeftIcon: {
    paddingLeft: 48,
  },
  inputWithRightIcon: {
    paddingRight: 48,
  },
  leftIconContainer: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  rightIconContainer: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
  },
  icon: {
    fontSize: 18,
  },
  leftIcon: {
    color: '#9CA3AF',
  },
  floatingLabel: {
    position: 'absolute',
    left: 16,
    top: 12,
    fontSize: 16,
    color: '#9CA3AF',
    backgroundColor: 'white',
    paddingHorizontal: 4,
  },
  floatingLabelActive: {
    top: -8,
    fontSize: 12,
    fontWeight: '500',
  },
  floatingLabelFocused: {
    color: '#3B82F6',
  },
  floatingLabelError: {
    color: '#EF4444',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    marginTop: 4,
  },
});

export default Input;
