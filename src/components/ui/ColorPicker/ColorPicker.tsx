import React from "react";
import { View, TouchableOpacity, Text, StyleSheet, Modal, Dimensions } from "react-native";
import { Ionicons } from "@expo/vector-icons";

const { width } = Dimensions.get("window");

export interface ColorOption {
    color: string;
    name: string;
    textColor?: string;
}

interface ColorPickerProps {
    visible: boolean;
    onClose: () => void;
    colors: ColorOption[];
    selectedColor: string;
    onColorSelect: (colorOption: ColorOption) => void;
    title?: string;
}

const ColorPicker: React.FC<ColorPickerProps> = ({
    visible,
    onClose,
    colors,
    selectedColor,
    onColorSelect,
    title = "Sayfa Rengi Seçin",
}) => {
    const handleColorSelect = (colorOption: ColorOption) => {
        onColorSelect(colorOption);
        onClose();
    };

    return (
        <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
            <View style={styles.overlay}>
                <TouchableOpacity style={styles.backdrop} onPress={onClose} activeOpacity={1} />

                <View style={styles.container}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>{title}</Text>
                        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                            <Ionicons name="close" size={24} color="#6b7280" />
                        </TouchableOpacity>
                    </View>

                    {/* Color Grid */}
                    <View style={styles.colorGrid}>
                        {colors.map((colorOption, index) => {
                            const isSelected = selectedColor === colorOption.color;

                            return (
                                <TouchableOpacity
                                    key={index}
                                    style={[
                                        styles.colorItem,
                                        { backgroundColor: colorOption.color },
                                        isSelected && styles.selectedColorItem,
                                    ]}
                                    onPress={() => handleColorSelect(colorOption)}
                                    activeOpacity={0.8}
                                >
                                    <View style={styles.colorContent}>
                                        {isSelected && (
                                            <Ionicons
                                                name="checkmark"
                                                size={20}
                                                color={colorOption.textColor || "#3b82f6"}
                                            />
                                        )}
                                        <Text
                                            style={[
                                                styles.colorName,
                                                { color: colorOption.textColor || "#374151" },
                                            ]}
                                        >
                                            {colorOption.name}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            );
                        })}
                    </View>

                    {/* Preview */}
                    {/* <View style={styles.previewSection}>
                        <Text style={styles.previewLabel}>Önizleme:</Text>
                        <View style={[styles.previewBox, { backgroundColor: selectedColor }]}>
                            <Text
                                style={[
                                    styles.previewText,
                                    {
                                        color:
                                            colors.find((c) => c.color === selectedColor)
                                                ?.textColor || "#374151",
                                    },
                                ]}
                            >
                                Örnek metin görünümü
                            </Text>
                        </View>
                    </View> */}
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        justifyContent: "center",
        alignItems: "center",
    },
    backdrop: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    container: {
        backgroundColor: "#ffffff",
        borderRadius: 16,
        padding: 20,
        width: width * 0.9,
        maxWidth: 400,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 20,
    },
    title: {
        fontSize: 18,
        fontWeight: "600",
        color: "#1f2937",
    },
    closeButton: {
        padding: 4,
    },
    colorGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
        marginBottom: 20,
    },
    colorItem: {
        width: "48%",
        height: 60,
        borderRadius: 12,
        marginBottom: 12,
        borderWidth: 2,
        borderColor: "transparent",
        justifyContent: "center",
        alignItems: "center",
    },
    selectedColorItem: {
        borderColor: "#3b82f6",
        borderWidth: 3,
    },
    colorContent: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
    },
    colorName: {
        fontSize: 14,
        fontWeight: "500",
    },
    previewSection: {
        marginTop: 10,
    },
    previewLabel: {
        fontSize: 14,
        fontWeight: "500",
        color: "#6b7280",
        marginBottom: 8,
    },
    previewBox: {
        padding: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: "#e5e7eb",
    },
    previewText: {
        fontSize: 16,
        lineHeight: 24,
        textAlign: "center",
    },
});

export default ColorPicker;
