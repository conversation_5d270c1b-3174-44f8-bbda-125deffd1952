import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';

export interface HeaderButtonProps {
    title?: string;
    onPress: () => void;
    variant?: 'default' | 'active' | 'ghost';
    icon?: string | React.ReactNode;
    disabled?: boolean;
    style?: ViewStyle;
    textStyle?: TextStyle;
}

const HeaderButton: React.FC<HeaderButtonProps> = ({ title, onPress, variant = 'default', icon, disabled = false, style, textStyle }) => {
    const getVariantStyles = () => {
        switch (variant) {
            case 'active':
                return {
                    backgroundColor: '#667eea',
                    borderColor: '#667eea',
                    textColor: '#FFFFFF'
                };
            case 'ghost':
                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    textColor: '#FFFFFF'
                };
            default:
                return {
                    backgroundColor: '#FFFFFF',
                    borderColor: '#dee2e6',
                    textColor: '#495057'
                };
        }
    };

    const variantStyles = getVariantStyles();

    const buttonStyle = [
        styles.button,
        {
            backgroundColor: variantStyles.backgroundColor,
            borderColor: variantStyles.borderColor,
            opacity: disabled ? 0.6 : 1
        },
        style
    ];

    const textStyles = [
        styles.buttonText,
        {
            color: variantStyles.textColor
        },
        textStyle
    ];

    return (
        <TouchableOpacity style={buttonStyle} onPress={onPress} disabled={disabled} activeOpacity={0.8}>
            {icon && typeof icon === 'string' ? <Text style={[textStyles, styles.icon]}>{icon}</Text> : icon}
            <Text style={textStyles}>{title}</Text>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 6,
        paddingVertical: 6,
        borderRadius: 6,
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2
    },
    buttonText: {
        fontSize: 14,
        fontWeight: '600'
    },
    icon: {
        marginRight: 4
    }
});

export default HeaderButton;
