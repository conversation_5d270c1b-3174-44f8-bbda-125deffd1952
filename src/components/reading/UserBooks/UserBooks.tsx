import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    FlatList,
    Alert,
    RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { bookService, UserBook } from '@services/firestore';
import { authService } from '@services/auth';
import Loading from '../../ui/Loading';

export interface UserBooksProps {
    onBookSelect: (book: UserBook) => void;
    onRefresh?: () => void;
}

const UserBooks: React.FC<UserBooksProps> = ({ onBookSelect, onRefresh }) => {
    const [books, setBooks] = useState<UserBook[]>([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        loadUserBooks();
    }, []);

    const loadUserBooks = async () => {
        try {
            const currentUser = authService.getCurrentUser();
            if (!currentUser) {
                setLoading(false);
                return;
            }

            const userBooks = await bookService.getUserBooks(currentUser.uid);
            setBooks(userBooks);
        } catch (error) {
            console.error('Error loading user books:', error);
            Alert.alert('Hata', 'Kitaplar yüklenirken bir hata oluştu.');
        } finally {
            setLoading(false);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await loadUserBooks();
        setRefreshing(false);
        onRefresh?.();
    };

    const handleDeleteBook = async (book: UserBook) => {
        Alert.alert(
            'Kitabı Sil',
            `"${book.title}" kitabını silmek istediğinizden emin misiniz?`,
            [
                { text: 'İptal', style: 'cancel' },
                {
                    text: 'Sil',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            if (book.id) {
                                await bookService.deleteUserBook(book.id);
                                await loadUserBooks();
                                Alert.alert('Başarılı', 'Kitap silindi.');
                            }
                        } catch (error) {
                            console.error('Error deleting book:', error);
                            Alert.alert('Hata', 'Kitap silinirken bir hata oluştu.');
                        }
                    },
                },
            ]
        );
    };

    const formatDate = (timestamp: any) => {
        if (!timestamp) return '';
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        return date.toLocaleDateString('tr-TR', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
        });
    };

    const getDifficultyColor = (difficulty: string) => {
        const colors = {
            A1: '#10b981', // Green
            A2: '#3b82f6', // Blue
            B1: '#f59e0b', // Yellow
            B2: '#f97316', // Orange
            C1: '#ef4444', // Red
            C2: '#7c3aed', // Purple
        };
        return colors[difficulty as keyof typeof colors] || '#6b7280';
    };

    const renderBookItem = ({ item }: { item: UserBook }) => (
        <TouchableOpacity
            style={styles.bookCard}
            onPress={() => onBookSelect(item)}
            activeOpacity={0.7}
        >
            <LinearGradient
                colors={['#ffffff', '#f8fafc']}
                style={styles.cardGradient}
            >
                <View style={styles.bookHeader}>
                    <View style={styles.bookInfo}>
                        <Text style={styles.bookTitle} numberOfLines={2}>
                            {item.title}
                        </Text>
                        <Text style={styles.bookFilename} numberOfLines={1}>
                            {item.originalFilename}
                        </Text>
                    </View>
                    <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={() => handleDeleteBook(item)}
                    >
                        <Ionicons name="trash-outline" size={20} color="#ef4444" />
                    </TouchableOpacity>
                </View>

                <View style={styles.bookMeta}>
                    <View style={styles.metaRow}>
                        <View style={[
                            styles.difficultyBadge,
                            { backgroundColor: getDifficultyColor(item.difficulty) }
                        ]}>
                            <Text style={styles.difficultyText}>{item.difficulty}</Text>
                        </View>
                        <Text style={styles.pageCount}>{item.pages || 0} sayfa</Text>
                    </View>

                    <View style={styles.metaRow}>
                        <Text style={styles.uploadDate}>
                            {formatDate(item.uploadDate)}
                        </Text>
                        {(item.readingProgress && item.readingProgress > 0) ? (
                            <Text style={styles.progress}>
                                %{Math.round(item.readingProgress || 0)} okundu
                            </Text>
                        ) : null}
                    </View>
                </View>

                {item.processingStatus === 'processing' && (
                    <View style={styles.processingBanner}>
                        <Ionicons name="hourglass-outline" size={16} color="#f59e0b" />
                        <Text style={styles.processingText}>İşleniyor...</Text>
                    </View>
                )}

                {item.processingStatus === 'failed' && (
                    <View style={styles.errorBanner}>
                        <Ionicons name="warning-outline" size={16} color="#ef4444" />
                        <Text style={styles.errorText}>İşleme hatası</Text>
                    </View>
                )}
            </LinearGradient>
        </TouchableOpacity>
    );

    const renderEmptyState = () => (
        <View style={styles.emptyState}>
            <Ionicons name="library-outline" size={64} color="#9ca3af" />
            <Text style={styles.emptyTitle}>Henüz kitap yüklenmemiş</Text>
            <Text style={styles.emptySubtitle}>
                PDF dosyalarınızı yükleyerek kendi kitap koleksiyonunuzu oluşturun
            </Text>
        </View>
    );

    if (loading) {
        return (
            <Loading
                visible={true}
                text="Kitaplar yükleniyor..."
                overlay={false}
            />
        );
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={books}
                renderItem={renderBookItem}
                keyExtractor={(item, index) => item.id || `book-${index}`}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContainer}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={['#3b82f6']}
                        tintColor="#3b82f6"
                    />
                }
                ListEmptyComponent={renderEmptyState}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    listContainer: {
        padding: 16,
        paddingBottom: 100, // Extra space for bottom navigation
    },
    bookCard: {
        marginBottom: 16,
        borderRadius: 12,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardGradient: {
        padding: 16,
    },
    bookHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    bookInfo: {
        flex: 1,
        marginRight: 12,
    },
    bookTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 4,
    },
    bookFilename: {
        fontSize: 14,
        color: '#6b7280',
    },
    deleteButton: {
        padding: 4,
    },
    bookMeta: {
        gap: 8,
    },
    metaRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    difficultyBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    difficultyText: {
        fontSize: 12,
        fontWeight: '600',
        color: 'white',
    },
    pageCount: {
        fontSize: 14,
        color: '#374151',
    },
    uploadDate: {
        fontSize: 12,
        color: '#9ca3af',
    },
    progress: {
        fontSize: 12,
        color: '#059669',
        fontWeight: '500',
    },
    processingBanner: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
        padding: 8,
        backgroundColor: '#fef3c7',
        borderRadius: 6,
    },
    processingText: {
        fontSize: 12,
        color: '#92400e',
        marginLeft: 4,
    },
    errorBanner: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
        padding: 8,
        backgroundColor: '#fee2e2',
        borderRadius: 6,
    },
    errorText: {
        fontSize: 12,
        color: '#dc2626',
        marginLeft: 4,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#374151',
        marginTop: 16,
        marginBottom: 8,
    },
    emptySubtitle: {
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 20,
        paddingHorizontal: 32,
    },
});

export default UserBooks;
