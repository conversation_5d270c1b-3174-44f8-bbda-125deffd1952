import React, { useEffect, useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, Alert } from "react-native";
import Tooltip from "react-native-walkthrough-tooltip";
import { Ionicons } from "@expo/vector-icons";
import { deepLTranslationService, TranslationResult } from "@services/translation";
import { firestoreService } from "@services/firestore";
import { authService } from "@services/auth";


interface InteractiveTextProps {
    text: string;
    style?: any;
    fontSize?: number;
    lineHeight?: number;
    color?: string;
    onLoad?: () => void;
}

interface TooltipData {
    word: string;
    translation: TranslationResult | null;
    loading: boolean;
}

interface Token {
    id: string;
    word: string;
    isWord: boolean;
    type: 'word' | 'punctuation' | 'whitespace' | 'line-break' | 'paragraph-break';
    originalIndex: number;
}

const InteractiveText: React.FC<InteractiveTextProps> = ({
    text,
    style,
    fontSize = 16,
    lineHeight = 24,
    color = "#212529",
    onLoad
}) => {
    const [tooltip, setTooltip] = useState<TooltipData | null>(null);
    const [showTooltip, setShowTooltip] = useState(false);
    const [selectedWord, setSelectedWord] = useState<string | null>(null);
    const [tokens, setTokens] = useState<Array<Token>>([]);

    // Process text and prepare tokens when component mounts or text changes
    useEffect(() => {
        const processText = async () => {
            const startTime = Date.now();

            // Add a small delay to prevent UI freezing during tokenization
            await new Promise(resolve => setTimeout(resolve, 50));

            const processedTokens = tokenizeText(text);
            setTokens(processedTokens);

            // Ensure minimum loading time for smooth UX (at least 300ms)
            const elapsedTime = Date.now() - startTime;
            const minLoadingTime = 300;
            if (elapsedTime < minLoadingTime) {
                await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
            }

            // Call onLoad callback when content is fully ready
            if (onLoad) {
                onLoad();
            }
        };

        processText();
    }, [text, onLoad]);

    // Enhanced text tokenization with comprehensive text processing
    const tokenizeText = (inputText: string): Array<Token> => {
        const tokens: Array<Token> = [];
        let offset = 0;

        // Process paragraph breaks (double line breaks)
        let paragraphMatch;
        const paragraphRegex = /\n\s*\n/g;
        while ((paragraphMatch = paragraphRegex.exec(inputText)) !== null) {
            const beforeParagraph = inputText.slice(offset, paragraphMatch.index);

            // Process text before paragraph break
            if (beforeParagraph) {
                tokens.push(...processTextSegment(beforeParagraph, offset));
            }

            // Add paragraph break token
            tokens.push({
                id: `paragraph_${paragraphMatch.index}`,
                word: paragraphMatch[0],
                isWord: false,
                type: 'paragraph-break',
                originalIndex: paragraphMatch.index
            });

            offset = paragraphMatch.index + paragraphMatch[0].length;
        }

        // Process remaining text after last paragraph break
        const finalText = inputText.slice(offset);
        if (finalText) {
            tokens.push(...processTextSegment(finalText, offset));
        }

        return tokens;
    };

    // Helper function to process text segments
    const processTextSegment = (text: string, baseOffset: number): Array<Token> => {
        const segmentTokens: Array<Token> = [];

        // Process line breaks first
        const lineBreakRegex = /\n/g;
        let lineMatch;
        let lastIndex = 0;

        while ((lineMatch = lineBreakRegex.exec(text)) !== null) {
            // Process text before line break
            const beforeLine = text.slice(lastIndex, lineMatch.index);
            if (beforeLine) {
                segmentTokens.push(...processTextLine(beforeLine, baseOffset + lastIndex));
            }

            // Add line break token
            segmentTokens.push({
                id: `line_${baseOffset + lineMatch.index}`,
                word: '\n',
                isWord: false,
                type: 'line-break',
                originalIndex: baseOffset + lineMatch.index
            });

            lastIndex = lineMatch.index + 1;
        }

        // Process remaining text after last line break
        const remainingLine = text.slice(lastIndex);
        if (remainingLine) {
            segmentTokens.push(...processTextLine(remainingLine, baseOffset + lastIndex));
        }

        return segmentTokens;
    };

    // Helper function to process individual lines
    const processTextLine = (line: string, baseOffset: number): Array<Token> => {
        const lineTokens: Array<Token> = [];

        // Enhanced tokenization for words, punctuation, and whitespace
        const tokenRegex = /([\wçğıöşüÇĞIİÖŞÜ]+(?:[''-][\wçğıöşüÇĞIİÖŞÜ]+)*)|(\s+)|([.,;:!?"""''()[\]{}\-–—…]+)/g;
        let match;

        while ((match = tokenRegex.exec(line)) !== null) {
            const fullMatch = match[0];
            const absoluteIndex = baseOffset + match.index;

            if (match[1]) {
                // Word token (including contractions and hyphenated words)
                lineTokens.push({
                    id: `word_${absoluteIndex}_${fullMatch}`,
                    word: fullMatch,
                    isWord: true,
                    type: 'word',
                    originalIndex: absoluteIndex
                });
            } else if (match[2]) {
                // Whitespace token
                lineTokens.push({
                    id: `space_${absoluteIndex}`,
                    word: fullMatch,
                    isWord: false,
                    type: 'whitespace',
                    originalIndex: absoluteIndex
                });
            } else if (match[3]) {
                // Punctuation token
                lineTokens.push({
                    id: `punct_${absoluteIndex}_${encodeURIComponent(fullMatch)}`,
                    word: fullMatch,
                    isWord: false,
                    type: 'punctuation',
                    originalIndex: absoluteIndex
                });
            }
        }

        return lineTokens;
    };

    const handleWordPressIn = async (token: Token) => {
        // Sadece gerçek kelimeler için çeviri yap (en az 2 karakter)
        if (token?.word?.length < 2) return;

        // Set selected word for highlighting
        setSelectedWord(token?.id);

        setTooltip({
            word: token.word,
            translation: null,
            loading: true,
        });

        setShowTooltip(true);

        try {
            // Kelimeyi çevir
            const translation = await deepLTranslationService.translateWord(
                token.word?.toLowerCase()
            );
            setTooltip((prev) => (prev ? { ...prev, translation, loading: false } : null));
        } catch (error) {
            console.error("Translation error:", error);
            setTooltip((prev) =>
                prev
                    ? {
                          ...prev,
                          translation: {
                              word: token.word,
                              meaning: "Çeviri bulunamadı",
                              examples: [],
                              difficulty: "intermediate",
                          },
                          loading: false,
                      }
                    : null
            );
        }
    };

    const hideTooltip = () => {
        setShowTooltip(false);
        setTooltip(null);
        setSelectedWord(null);
    };

    const handleAddToWordList = async () => {
        if (!tooltip?.translation) return;

        try {
            const currentUser = authService.getCurrentUser();
            if (!currentUser) {
                Alert.alert("Hata", "Kelime eklemek için giriş yapmanız gerekiyor.");
                return;
            }

            await firestoreService.addUserWord(currentUser.uid, tooltip.translation);
            Alert.alert("Başarılı", "Kelime listesine eklendi!");
            hideTooltip();
        } catch (error) {
            console.error("Error adding word to list:", error);
            Alert.alert("Hata", "Kelime eklenirken bir hata oluştu.");
        }
    };

    const renderTooltipContent = () => {
        if (!tooltip) {
            return (
                <View style={styles.tooltipContent}>
                    <Text style={styles.loadingText}>Yükleniyor...</Text>
                </View>
            );
        }

        if (tooltip.loading) {
            return (
                <View style={styles.tooltipContent}>
                    <ActivityIndicator size="small" color="#3b82f6" />
                    <Text style={styles.loadingText}>Çevriliyor...</Text>
                </View>
            );
        }

        if (tooltip.translation) {
            return (
                <View style={styles.tooltipContent}>
                    <Text style={styles.translationText}>{tooltip.translation.meaning}</Text>
                    <View style={styles.buttonSeparator} />
                    <TouchableOpacity
                        style={styles.addToListButton}
                        onPress={handleAddToWordList}
                        activeOpacity={0.6}
                    >
                        <Ionicons name="add" size={12} color="#6b7280" />
                        <Text style={styles.addToListText}>Liste Ekle</Text>
                    </TouchableOpacity>
                </View>
            );
        }

        return (
            <View style={styles.tooltipContent}>
                <Text style={styles.loadingText}>Çeviri bulunamadı</Text>
            </View>
        );
    };

    return (
        <View style={[styles.container, style]}>
            <View style={styles.textContainer}>
                {tokens.map((token) => {
                    // Render different token types appropriately
                    switch (token.type) {
                        case 'word':
                            const isSelected = selectedWord === token.id;
                            return (
                                <Tooltip
                                    key={token.id}
                                    isVisible={showTooltip && isSelected}
                                    content={renderTooltipContent()}
                                    placement="top"
                                    onClose={hideTooltip}
                                    tooltipStyle={styles.tooltip}
                                    contentStyle={styles.tooltipContainer}
                                    showChildInTooltip={false}
                                    allowChildInteraction={false}
                                    disableShadow={false}
                                    backgroundColor="transparent"
                                >
                                    <TouchableOpacity
                                        onLongPress={() => handleWordPressIn(token)}
                                        activeOpacity={0.7}
                                        style={styles.wordTouchable}
                                    >
                                        <Text
                                            style={[
                                                styles.word,
                                                { fontSize, lineHeight, color },
                                                isSelected && styles.selectedWord,
                                            ]}
                                        >
                                            {token.word}
                                        </Text>
                                    </TouchableOpacity>
                                </Tooltip>
                            );

                        case 'paragraph-break':
                            return (
                                <View key={token.id} style={styles.paragraphBreak}>
                                    <Text style={[{ fontSize, lineHeight, color }]}>
                                        {'\n\n'}
                                    </Text>
                                </View>
                            );

                        case 'line-break':
                            return (
                                <View key={token.id} style={styles.lineBreak}>
                                    <Text style={[{ fontSize, lineHeight, color }]}>
                                        {'\n'}
                                    </Text>
                                </View>
                            );

                        case 'whitespace':
                            return (
                                <Text
                                    key={token.id}
                                    style={[styles.whitespace, { fontSize, lineHeight, color }]}
                                >
                                    {token.word}
                                </Text>
                            );

                        case 'punctuation':
                            return (
                                <Text
                                    key={token.id}
                                    style={[styles.punctuation, { fontSize, lineHeight, color }]}
                                >
                                    {token.word}
                                </Text>
                            );

                        default:
                            // Fallback for any unhandled token types
                            return (
                                <Text
                                    key={token.id}
                                    style={[styles.nonWord, { fontSize, lineHeight, color }]}
                                >
                                    {token.word}
                                </Text>
                            );
                    }
                })}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: "relative",
    },
    textContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
    },
    wordTouchable: {
        // TouchableOpacity için stil
    },
    word: {
        borderRadius: 4,
        paddingHorizontal: 2,
        paddingVertical: 1,
    },
    selectedWord: {
        backgroundColor: "#e0f2fe",
        borderRadius: 4,
    },
    nonWord: {
        // Boşluk ve noktalama işaretleri için stil
    },
    paragraphBreak: {
        width: '100%',
        height: 20, // Use line height from text for proper spacing
    },
    lineBreak: {
        width: '100%',
        height: 0, // Use line height from text for proper spacing
    },
    whitespace: {
        // Preserve whitespace as-is
    },
    punctuation: {
        // Punctuation styling
    },
    tooltip: {
        // shadowOpacity: 0,
    },
    tooltipContainer: {
        borderRadius: 8,
        padding: 0,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        elevation: 4,
        minWidth: "auto",
        maxWidth: 250,
        width: "100%",
        height: "auto",
    },
    tooltipContent: {
        paddingHorizontal: 12,
        paddingVertical: 10,
        alignItems: "center",
    },
    loadingText: {
        fontSize: 14,
        color: "#6B7280",
        textAlign: "center",
    },
    translationText: {
        fontSize: 15,
        color: "#1F2937",
        fontWeight: "600",
        textAlign: "center",
        lineHeight: 20,
    },
    buttonSeparator: {
        height: 1,
        backgroundColor: "#f3f4f6",
        width: "100%",
        marginVertical: 8,
    },
    addToListButton: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "transparent",
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
    },
    addToListText: {
        marginLeft: 4,
        fontSize: 12,
        color: "#6b7280",
        fontWeight: "400",
    },
});

export default InteractiveText;
