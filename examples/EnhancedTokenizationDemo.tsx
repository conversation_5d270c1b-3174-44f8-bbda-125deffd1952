/**
 * Enhanced Tokenization Demo
 * 
 * This example demonstrates the improved tokenization capabilities
 * of the InteractiveText component with various text scenarios.
 */

import React from 'react';
import { View, ScrollView, Text, StyleSheet } from 'react-native';
import InteractiveText from '../src/components/reading/InteractiveText';

const EnhancedTokenizationDemo = () => {
    // Test cases for different text scenarios
    const testCases = [
        {
            title: "English Text with Contractions",
            text: "Don't worry! I'll help you understand what's happening here. It's really quite simple."
        },
        {
            title: "Turkish Text with Special Characters",
            text: "Merhaba dünya! Türkçe karakterler: çğıöşü. Bu çok güzel bir test metnidir."
        },
        {
            title: "Hyphenated Words and Complex Punctuation",
            text: "The state-of-the-art technology is twenty-one years old. \"Amazing!\" she said."
        },
        {
            title: "Multiple Paragraphs with Line Breaks",
            text: "First paragraph here.\nSingle line break.\n\nSecond paragraph after double line break.\n\nThird paragraph with more content."
        },
        {
            title: "Mixed Formatting and Whitespace",
            text: "Normal text.    Multiple spaces here.\n\n  Indented paragraph.\n\nFinal paragraph."
        },
        {
            title: "Complex Punctuation and Symbols",
            text: "Various punctuation: comma, semicolon; colon: exclamation! question? \"quotes\" 'apostrophes' (parentheses) [brackets] {braces} dash—em-dash–en-dash ellipsis…"
        },
        {
            title: "Turkish and English Mixed",
            text: "Hello merhaba! This is İngilizce and Türkçe mixed text. Don't you think it's çok güzel?"
        }
    ];

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.header}>Enhanced Tokenization Demo</Text>
            <Text style={styles.description}>
                This demo shows how the enhanced tokenization system handles various text scenarios:
            </Text>

            {testCases.map((testCase, index) => (
                <View key={index} style={styles.testCase}>
                    <Text style={styles.testTitle}>{testCase.title}</Text>
                    
                    <View style={styles.originalText}>
                        <Text style={styles.label}>Original Text:</Text>
                        <Text style={styles.rawText}>{JSON.stringify(testCase.text)}</Text>
                    </View>

                    <View style={styles.renderedText}>
                        <Text style={styles.label}>Rendered with Enhanced Tokenization:</Text>
                        <View style={styles.textContainer}>
                            <InteractiveText
                                text={testCase.text}
                                fontSize={16}
                                lineHeight={24}
                                color="#374151"
                            />
                        </View>
                    </View>
                </View>
            ))}

            <View style={styles.features}>
                <Text style={styles.featuresTitle}>Enhanced Features:</Text>
                <Text style={styles.feature}>✅ Turkish character support (çğıöşüÇĞIİÖŞÜ)</Text>
                <Text style={styles.feature}>✅ Contractions (don't, won't, I'll)</Text>
                <Text style={styles.feature}>✅ Hyphenated words (state-of-the-art)</Text>
                <Text style={styles.feature}>✅ Paragraph breaks (double line breaks)</Text>
                <Text style={styles.feature}>✅ Line breaks (single line breaks)</Text>
                <Text style={styles.feature}>✅ Multiple whitespace preservation</Text>
                <Text style={styles.feature}>✅ Complex punctuation handling</Text>
                <Text style={styles.feature}>✅ Unique token identification</Text>
                <Text style={styles.feature}>✅ Performance optimized processing</Text>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
        padding: 20,
    },
    header: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 10,
        textAlign: 'center',
    },
    description: {
        fontSize: 16,
        color: '#6b7280',
        marginBottom: 20,
        textAlign: 'center',
        lineHeight: 24,
    },
    testCase: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    testTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#1f2937',
        marginBottom: 12,
    },
    originalText: {
        marginBottom: 16,
    },
    renderedText: {
        marginBottom: 8,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: '#374151',
        marginBottom: 8,
    },
    rawText: {
        fontSize: 12,
        fontFamily: 'monospace',
        color: '#6b7280',
        backgroundColor: '#f3f4f6',
        padding: 8,
        borderRadius: 6,
    },
    textContainer: {
        backgroundColor: '#f9fafb',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e5e7eb',
    },
    features: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 20,
    },
    featuresTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#1f2937',
        marginBottom: 12,
    },
    feature: {
        fontSize: 14,
        color: '#374151',
        marginBottom: 6,
        lineHeight: 20,
    },
});

export default EnhancedTokenizationDemo;
