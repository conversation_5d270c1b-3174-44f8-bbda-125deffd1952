/**
 * Example: Loading Component Integration
 * 
 * This file demonstrates how to integrate the new Loading component
 * into existing screens to replace custom loading implementations.
 */

import React, { useState } from 'react';
import { View, TouchableOpacity, Text, Alert } from 'react-native';
import { Loading } from '../src/components/ui';
import { authService, firestoreService } from '../src/services';

// Example 1: Login Screen Integration
export const LoginScreenExample = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    setLoading(true);
    try {
      await authService.signIn(email, password);
      // Navigation will be handled by auth state change
    } catch (error: any) {
      Alert.alert('Giriş Hatası', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {/* Login form components here */}
      
      <TouchableOpacity onPress={handleLogin} disabled={loading}>
        <Text>{loading ? '<PERSON><PERSON><PERSON> yapılıyor...' : '<PERSON><PERSON><PERSON> Yap'}</Text>
      </TouchableOpacity>

      {/* Replace custom loading with Loading component */}
      <Loading 
        visible={loading}
        text="Giriş yapılıyor..."
        color="#3B82F6"
      />
    </View>
  );
};

// Example 2: WordList Screen Integration
export const WordListScreenExample = () => {
  const [words, setWords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const loadWords = async () => {
    setLoading(true);
    try {
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        const userWords = await firestoreService.getUserWords(currentUser.uid);
        setWords(userWords);
      }
    } catch (error) {
      Alert.alert('Hata', 'Kelimeler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const deleteWord = async (wordId: string) => {
    setDeleteLoading(true);
    try {
      await firestoreService.deleteUserWord(wordId);
      await loadWords(); // Reload words
      Alert.alert('Başarılı', 'Kelime silindi!');
    } catch (error) {
      Alert.alert('Hata', 'Kelime silinirken bir hata oluştu');
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {/* Word list content */}
      
      {/* Multiple loading states */}
      <Loading 
        visible={loading}
        text="Kelimeler yükleniyor..."
        color="#3B82F6"
      />
      
      <Loading 
        visible={deleteLoading}
        text="Kelime siliniyor..."
        color="#ef4444"
      />
    </View>
  );
};

// Example 3: Inline Loading for Specific Sections
export const InlineLoadingExample = () => {
  const [sectionLoading, setSectionLoading] = useState(false);

  const loadSectionData = async () => {
    setSectionLoading(true);
    try {
      // Load specific section data
      await new Promise(resolve => setTimeout(resolve, 2000));
    } finally {
      setSectionLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, padding: 20 }}>
      <Text style={{ fontSize: 18, marginBottom: 20 }}>Section Title</Text>
      
      {/* Inline loading for specific section */}
      <View style={{ height: 200, backgroundColor: '#f5f5f5', borderRadius: 8 }}>
        <Loading 
          visible={sectionLoading}
          text="Bölüm yükleniyor..."
          overlay={false}  // Inline mode
          size="small"
        />
        
        {!sectionLoading && (
          <Text style={{ padding: 20 }}>Section content here...</Text>
        )}
      </View>
      
      <TouchableOpacity 
        onPress={loadSectionData}
        style={{ 
          backgroundColor: '#3B82F6', 
          padding: 12, 
          borderRadius: 8, 
          marginTop: 20 
        }}
      >
        <Text style={{ color: 'white', textAlign: 'center' }}>
          Reload Section
        </Text>
      </TouchableOpacity>
    </View>
  );
};

// Example 4: Custom Styled Loading
export const CustomStyledLoadingExample = () => {
  const [loading, setLoading] = useState(false);

  const handleCustomOperation = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, justifyContent: 'center', padding: 20 }}>
      <TouchableOpacity 
        onPress={handleCustomOperation}
        style={{ 
          backgroundColor: '#10b981', 
          padding: 16, 
          borderRadius: 12 
        }}
      >
        <Text style={{ color: 'white', textAlign: 'center', fontSize: 16 }}>
          Start Custom Operation
        </Text>
      </TouchableOpacity>

      {/* Custom styled loading */}
      <Loading 
        visible={loading}
        text="Özel işlem gerçekleştiriliyor..."
        color="#10b981"
        contentStyle={{
          backgroundColor: '#f0fdf4',
          borderColor: '#10b981',
          borderWidth: 2,
        }}
      />
    </View>
  );
};

// Example 5: Migration from existing custom loading
export const MigrationExample = () => {
  const [loading, setLoading] = useState(false);

  // BEFORE: Custom loading implementation
  const renderOldLoading = () => {
    if (!loading) return null;
    
    return (
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <View style={{
          backgroundColor: 'white',
          padding: 20,
          borderRadius: 10,
          alignItems: 'center'
        }}>
          {/* <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={{ marginTop: 10 }}>Yükleniyor...</Text> */}
        </View>
      </View>
    );
  };

  // AFTER: Using Loading component
  return (
    <View style={{ flex: 1 }}>
      {/* Your screen content */}
      
      {/* Replace the entire custom loading with this single component */}
      <Loading 
        visible={loading}
        text="Yükleniyor..."
      />
    </View>
  );
};
