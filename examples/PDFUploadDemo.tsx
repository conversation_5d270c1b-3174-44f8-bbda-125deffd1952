/**
 * PDF Upload System Demo
 * 
 * This example demonstrates the complete PDF upload and processing workflow
 * in the LingoTexts app, including file selection, processing, and integration
 * with the reading system.
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { PDFUpload } from '../src/components/ui';
import UserBooks from '../src/components/reading/UserBooks';
import { UserBook } from '../src/services/firestore';

const PDFUploadDemo = () => {
    const [refreshKey, setRefreshKey] = useState(0);

    // Handle successful book upload
    const handleUploadComplete = (book: UserBook) => {
        Alert.alert(
            'Upload Successful!',
            `"${book.title}" has been uploaded and processed successfully.`,
            [
                {
                    text: 'Read Now',
                    onPress: () => {
                        // Navigate to reading view
                        console.log('Navigate to reading view:', book);
                    }
                },
                {
                    text: 'OK',
                    style: 'default'
                }
            ]
        );
        
        // Refresh the books list
        setRefreshKey(prev => prev + 1);
    };

    // Handle upload errors
    const handleUploadError = (error: string) => {
        Alert.alert('Upload Error', error);
    };

    // Handle book selection for reading
    const handleBookSelect = (book: UserBook) => {
        console.log('Selected book for reading:', book);
        // In a real app, this would navigate to ReadingView
        Alert.alert(
            'Open Book',
            `Opening "${book.title}" for reading...`,
            [{ text: 'OK' }]
        );
    };

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>PDF Upload System Demo</Text>
            <Text style={styles.description}>
                This demo shows the complete PDF upload and processing workflow:
            </Text>

            {/* Upload Section */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>📤 Upload New PDF</Text>
                <Text style={styles.sectionDescription}>
                    Click the button below to upload a PDF file. The system will:
                </Text>
                
                <View style={styles.featureList}>
                    <Text style={styles.feature}>• Validate the PDF file format and size</Text>
                    <Text style={styles.feature}>• Extract text content from the PDF</Text>
                    <Text style={styles.feature}>• Auto-generate metadata (title, difficulty, word count)</Text>
                    <Text style={styles.feature}>• Allow you to edit book information</Text>
                    <Text style={styles.feature}>• Save the book to your personal collection</Text>
                </View>

                <View style={styles.uploadContainer}>
                    <PDFUpload
                        onUploadComplete={handleUploadComplete}
                        onUploadError={handleUploadError}
                    />
                </View>
            </View>

            {/* User Books Section */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>📚 Your Uploaded Books</Text>
                <Text style={styles.sectionDescription}>
                    Your uploaded books will appear below. You can:
                </Text>
                
                <View style={styles.featureList}>
                    <Text style={styles.feature}>• View all your uploaded books</Text>
                    <Text style={styles.feature}>• See reading progress for each book</Text>
                    <Text style={styles.feature}>• Delete books you no longer need</Text>
                    <Text style={styles.feature}>• Open books for reading with interactive features</Text>
                </View>

                <View style={styles.booksContainer}>
                    <UserBooks
                        key={refreshKey}
                        onBookSelect={handleBookSelect}
                        onRefresh={() => setRefreshKey(prev => prev + 1)}
                    />
                </View>
            </View>

            {/* Features Overview */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>✨ System Features</Text>
                
                <View style={styles.featureCategory}>
                    <Text style={styles.categoryTitle}>File Processing:</Text>
                    <Text style={styles.feature}>• PDF format validation</Text>
                    <Text style={styles.feature}>• 10MB file size limit</Text>
                    <Text style={styles.feature}>• Text extraction and cleaning</Text>
                    <Text style={styles.feature}>• Auto-difficulty detection</Text>
                </View>

                <View style={styles.featureCategory}>
                    <Text style={styles.categoryTitle}>User Experience:</Text>
                    <Text style={styles.feature}>• Real-time upload progress</Text>
                    <Text style={styles.feature}>• Metadata editing interface</Text>
                    <Text style={styles.feature}>• Book management tools</Text>
                    <Text style={styles.feature}>• Reading progress tracking</Text>
                </View>

                <View style={styles.featureCategory}>
                    <Text style={styles.categoryTitle}>Reading Integration:</Text>
                    <Text style={styles.feature}>• Same interactive features as default books</Text>
                    <Text style={styles.feature}>• Word translation tooltips</Text>
                    <Text style={styles.feature}>• Word list integration</Text>
                    <Text style={styles.feature}>• Reading customization options</Text>
                </View>
            </View>

            {/* Technical Details */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>🔧 Technical Implementation</Text>
                
                <View style={styles.techDetail}>
                    <Text style={styles.techTitle}>Services Used:</Text>
                    <Text style={styles.techItem}>• PDFProcessorService - File processing</Text>
                    <Text style={styles.techItem}>• FirestoreService - Data storage</Text>
                    <Text style={styles.techItem}>• AuthService - User authentication</Text>
                </View>

                <View style={styles.techDetail}>
                    <Text style={styles.techTitle}>Components:</Text>
                    <Text style={styles.techItem}>• PDFUpload - Upload interface</Text>
                    <Text style={styles.techItem}>• UserBooks - Book management</Text>
                    <Text style={styles.techItem}>• Loading - Progress feedback</Text>
                </View>

                <View style={styles.techDetail}>
                    <Text style={styles.techTitle}>Data Flow:</Text>
                    <Text style={styles.techItem}>1. File selection and validation</Text>
                    <Text style={styles.techItem}>2. PDF text extraction</Text>
                    <Text style={styles.techItem}>3. Metadata generation</Text>
                    <Text style={styles.techItem}>4. User editing and confirmation</Text>
                    <Text style={styles.techItem}>5. Firestore storage</Text>
                    <Text style={styles.techItem}>6. Reading integration</Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#1f2937',
        textAlign: 'center',
        marginVertical: 20,
    },
    description: {
        fontSize: 16,
        color: '#6b7280',
        textAlign: 'center',
        marginHorizontal: 20,
        marginBottom: 30,
        lineHeight: 24,
    },
    section: {
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginBottom: 20,
        borderRadius: 12,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 8,
    },
    sectionDescription: {
        fontSize: 14,
        color: '#6b7280',
        marginBottom: 12,
        lineHeight: 20,
    },
    featureList: {
        marginBottom: 16,
    },
    feature: {
        fontSize: 14,
        color: '#374151',
        marginBottom: 4,
        lineHeight: 20,
    },
    uploadContainer: {
        alignItems: 'center',
        marginTop: 16,
    },
    booksContainer: {
        marginTop: 16,
        minHeight: 200,
    },
    featureCategory: {
        marginBottom: 16,
    },
    categoryTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2937',
        marginBottom: 8,
    },
    techDetail: {
        marginBottom: 16,
    },
    techTitle: {
        fontSize: 15,
        fontWeight: '600',
        color: '#374151',
        marginBottom: 6,
    },
    techItem: {
        fontSize: 13,
        color: '#6b7280',
        marginBottom: 3,
        marginLeft: 8,
    },
});

export default PDFUploadDemo;
