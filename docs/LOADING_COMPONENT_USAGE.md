# Loading Component Usage Guide

## Overview

The `Loading` component is a reusable loading indicator that can be used across the LingoTexts app to display loading states during asynchronous operations. It provides both overlay and inline display modes with customizable styling.

## Features

- **Overlay Mode**: Full-screen modal overlay with semi-transparent backdrop
- **Inline Mode**: Embedded loading indicator for specific sections
- **Customizable Text**: Support for custom loading messages
- **Consistent Styling**: Follows the app's design system and theme colors
- **TypeScript Support**: Full TypeScript interface definitions
- **Cross-Platform**: Works on both iOS and Android

## Basic Usage

### Import the Component

```typescript
import { Loading } from '../components/ui';
// or
import Loading from '../components/ui/Loading';
```

### Overlay Loading (Default)

```typescript
import React, { useState } from 'react';
import { View, Button } from 'react-native';
import { Loading } from '../components/ui';

const MyScreen = () => {
  const [loading, setLoading] = useState(false);

  const handleAsyncOperation = async () => {
    setLoading(true);
    try {
      // Perform async operation
      await someAsyncFunction();
    } finally {
      setLoading(false);
    }
  };

  return (
    <View>
      <Button title="Start Loading" onPress={handleAsyncOperation} />
      
      <Loading 
        visible={loading}
        text="İşlem gerçekleştiriliyor..."
      />
    </View>
  );
};
```

### Inline Loading

```typescript
const MyComponent = () => {
  const [loading, setLoading] = useState(false);

  return (
    <View>
      <Loading 
        visible={loading}
        text="Veriler yükleniyor..."
        overlay={false}
      />
    </View>
  );
};
```

## Props Interface

```typescript
interface LoadingProps {
  visible: boolean;           // Whether the loading overlay is visible
  text?: string;             // Loading text (default: "Yükleniyor...")
  size?: 'small' | 'large';  // Spinner size (default: 'large')
  color?: string;            // Spinner color (default: '#3B82F6')
  overlay?: boolean;         // Modal overlay mode (default: true)
  containerStyle?: object;   // Custom container styling
  contentStyle?: object;     // Custom content styling
}
```

## Advanced Usage Examples

### Custom Styling

```typescript
<Loading 
  visible={loading}
  text="Özel yükleme mesajı"
  color="#10b981"
  size="small"
  contentStyle={{
    backgroundColor: '#f8fafc',
    borderRadius: 12,
  }}
/>
```

### Different Loading States

```typescript
const MyScreen = () => {
  const [authLoading, setAuthLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);

  return (
    <View>
      {/* Authentication loading */}
      <Loading 
        visible={authLoading}
        text="Giriş yapılıyor..."
        color="#3B82F6"
      />
      
      {/* Data loading */}
      <Loading 
        visible={dataLoading}
        text="Veriler senkronize ediliyor..."
        color="#10b981"
      />
    </View>
  );
};
```

### Integration with Existing Screens

#### LoginScreen Integration

```typescript
// In LoginScreen.tsx
import { Loading } from '../components/ui';

const LoginScreen = () => {
  const [loading, setLoading] = useState(false);

  const handleLogin = async (email: string, password: string) => {
    setLoading(true);
    try {
      await authService.signIn(email, password);
    } catch (error) {
      // Handle error
    } finally {
      setLoading(false);
    }
  };

  return (
    <View>
      {/* Login form */}
      
      <Loading 
        visible={loading}
        text="Giriş yapılıyor..."
      />
    </View>
  );
};
```

#### WordListScreen Integration

```typescript
// In WordListScreen.tsx
import { Loading } from '../components/ui';

const WordListScreen = () => {
  const [loading, setLoading] = useState(false);

  const loadWords = async () => {
    setLoading(true);
    try {
      const words = await firestoreService.getUserWords(userId);
      setWords(words);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View>
      {/* Word list content */}
      
      <Loading 
        visible={loading}
        text="Kelimeler yükleniyor..."
      />
    </View>
  );
};
```

## Design System Integration

The Loading component follows the LingoTexts design system:

- **Colors**: Uses primary blue (#3B82F6) by default
- **Typography**: 16px font size with medium weight
- **Shadows**: Consistent shadow styling with other UI components
- **Border Radius**: 16px radius matching the app's design language
- **Spacing**: Consistent padding and margins

## Best Practices

1. **Always set loading to false**: Use try/finally blocks to ensure loading state is reset
2. **Meaningful messages**: Use descriptive loading text for better UX
3. **Appropriate timing**: Show loading for operations taking >500ms
4. **Avoid nested overlays**: Don't show multiple overlay loadings simultaneously
5. **Accessibility**: The component includes proper accessibility features

## Common Use Cases

- User authentication (login, signup, logout)
- Data fetching from APIs or Firestore
- File uploads/downloads
- Sync operations
- Form submissions
- Navigation transitions with data loading

## Migration from Existing Loading

If you're currently using custom loading implementations, you can easily migrate:

```typescript
// Before
{loading && (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#3B82F6" />
    <Text>Loading...</Text>
  </View>
)}

// After
<Loading 
  visible={loading}
  text="Loading..."
/>
```
