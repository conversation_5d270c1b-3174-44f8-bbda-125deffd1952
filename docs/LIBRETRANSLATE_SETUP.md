# LibreTranslate Entegrasyonu

LingoTexts uygulaması artık LibreTranslate açık kaynak çeviri servisi ile entegre edilmiştir.

## 🌟 Özellikler

- **Ücretsiz**: LibreTranslate tamamen ücretsiz ve açık kaynak
- **Gizlilik**: Verileriniz üçüncü taraf şirketlerle paylaşılmaz
- **Fallback Mekanizması**: Birden fazla LibreTranslate instance'ı dener
- **Akıllı Çeviri**: Başarısız olan servisleri atlar, çalışan olanı kullanır

## 🚀 Kullanılan Servisler

### Ana LibreTranslate Instance'ları
1. **libretranslate.de** (Ana)
2. **translate.argosopentech.com** (Yedek 1)
3. **libretranslate.com** (Yedek 2)
4. **Google Translate** (Son çare)

## 📱 InteractiveText Kullanımı

```typescript
import { InteractiveText } from '../components/reading';

<InteractiveText
    text="Your English text here"
    fontSize={16}
    lineHeight={24}
    color="#212529"
/>
```

### Nasıl Çalışır?
1. Herhangi bir kelimeye **0.5 saniye** basılı tutun
2. Tooltip açılır ve çeviri başlar
3. LibreTranslate servisleri sırayla denenir
4. İlk başarılı çeviri gösterilir

## 🔧 Konfigürasyon

### Çevre Değişkenleri (Opsiyonel)
```bash
# .env dosyasına ekleyin
EXPO_PUBLIC_LIBRETRANSLATE_API_KEY=your_api_key_here
```

### Özel LibreTranslate Sunucusu
```typescript
import { LibreTranslateService } from '../services/translation';

const customService = new LibreTranslateService(
    "https://your-libretranslate-server.com",
    "your-api-key"
);
```

## 🛠️ Kendi LibreTranslate Sunucunuzu Kurun

### Docker ile
```bash
docker run -ti --rm -p 5000:5000 libretranslate/libretranslate
```

### Python ile
```bash
pip install libretranslate
libretranslate [args]
```

## 📊 Desteklenen Diller

LibreTranslate 30+ dili destekler:
- 🇹🇷 Türkçe (tr)
- 🇺🇸 İngilizce (en)
- 🇪🇸 İspanyolca (es)
- 🇫🇷 Fransızca (fr)
- 🇩🇪 Almanca (de)
- 🇮🇹 İtalyanca (it)
- 🇵🇹 Portekizce (pt)
- 🇷🇺 Rusça (ru)
- 🇨🇳 Çince (zh)
- 🇯🇵 Japonca (ja)
- Ve daha fazlası...

## 🔄 Fallback Mekanizması

SmartTranslationService şu sırayla dener:

1. **LibreTranslate.de** - Ana servis
2. **Argos Open Tech** - Yedek servis 1
3. **LibreTranslate.com** - Yedek servis 2
4. **Google Translate** - Son çare (API key gerekli)

Bir servis başarısız olursa otomatik olarak bir sonrakini dener.

## 🐛 Hata Ayıklama

### Console Logları
```javascript
// Başarısız servisler için uyarı
console.warn('Translation service failed, trying next...');

// Çeviri hataları için
console.error('Translation error:', error);
```

### Test Etme
```typescript
import { smartTranslationService } from '../services/translation';

// Kelime çevirisi test
const result = await smartTranslationService.translateWord('hello');
console.log(result.meaning); // "merhaba"

// Metin çevirisi test
const text = await smartTranslationService.translateText('Hello world');
console.log(text); // "Merhaba dünya"
```

## 🔒 Gizlilik ve Güvenlik

- LibreTranslate açık kaynak ve kendi sunucunuzda çalışabilir
- Verileriniz üçüncü taraf şirketlere gönderilmez
- API anahtarı gerektirmez (opsiyonel)
- HTTPS ile güvenli iletişim

## 📈 Performans

- **Hız**: Ortalama 1-3 saniye çeviri süresi
- **Doğruluk**: Temel çeviriler için %85-90 doğruluk
- **Kullanılabilirlik**: %99+ uptime (birden fazla instance sayesinde)

## 🤝 Katkıda Bulunma

LibreTranslate açık kaynak bir projedir:
- GitHub: https://github.com/LibreTranslate/LibreTranslate
- Dokümantasyon: https://libretranslate.com/docs
- API Referansı: https://libretranslate.com/docs/api

## 📞 Destek

Sorunlar için:
1. Console loglarını kontrol edin
2. Network bağlantısını kontrol edin
3. LibreTranslate sunucularının durumunu kontrol edin
4. GitHub Issues'da sorun bildirin
