# MainLayout System Documentation

## Overview

The MainLayout system provides a consistent, reusable layout structure for all main screens in the LingoTexts app. It standardizes padding, scrolling, background colors, gradients, and safe area handling across the application.

## Features

### 🎨 **Consistent Design**
- Standardized padding and spacing
- Consistent background colors and gradients
- Safe area handling for all devices
- Status bar configuration

### 📱 **Responsive Layout**
- Automatic scroll view management
- Keyboard handling
- Bottom navigation space allocation
- Flexible content arrangement

### ⚙️ **Customizable Options**
- Optional gradient backgrounds
- Configurable padding values
- Scrollable/non-scrollable content
- Custom styling support

## Component Structure

### **MainLayout Component**
```typescript
interface MainLayoutProps {
    children: React.ReactNode;
    scrollable?: boolean;
    showGradient?: boolean;
    gradientColors?: string[];
    backgroundColor?: string;
    contentStyle?: ViewStyle;
    scrollViewProps?: ScrollViewProps;
    safeAreaStyle?: ViewStyle;
    paddingHorizontal?: number;
    paddingTop?: number;
    paddingBottom?: number;
}
```

### **Default Values**
- `scrollable`: `true`
- `showGradient`: `false`
- `gradientColors`: `['#667eea', '#764ba2']`
- `backgroundColor`: `'#f8f9fa'`
- `paddingHorizontal`: `20`
- `paddingTop`: `20`
- `paddingBottom`: `100` (space for bottom navigation)

## Usage Examples

### **Basic Usage**
```typescript
import { MainLayout } from "../../components/layout";

const MyScreen = () => {
    return (
        <MainLayout>
            <Text>Your content here</Text>
        </MainLayout>
    );
};
```

### **Custom Background Color**
```typescript
<MainLayout backgroundColor="#f0f0f0">
    <Text>Content with custom background</Text>
</MainLayout>
```

### **With Gradient Background**
```typescript
<MainLayout 
    showGradient={true}
    gradientColors={['#667eea', '#764ba2']}
>
    <Text style={{ color: 'white' }}>Content with gradient</Text>
</MainLayout>
```

### **Non-Scrollable Content**
```typescript
<MainLayout scrollable={false}>
    <View style={{ flex: 1 }}>
        <Text>Fixed content that doesn't scroll</Text>
    </View>
</MainLayout>
```

### **Custom Padding**
```typescript
<MainLayout 
    paddingHorizontal={16}
    paddingTop={10}
    paddingBottom={80}
>
    <Text>Content with custom padding</Text>
</MainLayout>
```

## Implementation in Main Screens

### **Before MainLayout (Old Structure)**
```typescript
const HomeScreen = () => {
    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Content */}
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    scrollView: { flex: 1 },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120,
    },
});
```

### **After MainLayout (New Structure)**
```typescript
import { MainLayout } from "../../components/layout";

const HomeScreen = () => {
    return (
        <MainLayout backgroundColor="#f8fafc">
            {/* Content directly here */}
        </MainLayout>
    );
};

// No need for container, scrollView, content styles!
```

## Updated Screens

### **✅ HomeScreen**
- Removed: `container`, `scrollView`, `content` styles
- Added: `MainLayout` with `backgroundColor="#f8fafc"`
- Result: Cleaner code, consistent layout

### **✅ ReadingScreen**
- Removed: Manual `LinearGradient` and `ScrollView` wrapper
- Added: `MainLayout` with standard configuration
- Result: Simplified structure, better maintainability

### **✅ WordListScreen**
- Removed: Redundant layout styles
- Added: `MainLayout` for consistent spacing
- Result: Unified appearance with other screens

### **✅ ProfileScreen**
- Removed: Custom container and scroll management
- Added: `MainLayout` for standardized layout
- Result: Consistent user experience

### **✅ QuizScreen**
- Removed: Manual layout management
- Added: `MainLayout` for unified structure
- Result: Better code organization

## Benefits

### **🔧 For Developers**
1. **Reduced Boilerplate**: No need to repeat layout code
2. **Consistent Patterns**: Same structure across all screens
3. **Easy Maintenance**: Changes in one place affect all screens
4. **Type Safety**: Full TypeScript support with proper interfaces

### **👤 For Users**
1. **Consistent Experience**: Same spacing and behavior everywhere
2. **Better Performance**: Optimized scroll and layout handling
3. **Responsive Design**: Works well on all device sizes
4. **Smooth Navigation**: Consistent transitions between screens

### **📱 Technical Advantages**
1. **Safe Area Handling**: Automatic handling of notches and status bars
2. **Keyboard Management**: Built-in keyboard avoidance
3. **Memory Efficiency**: Optimized rendering and scroll performance
4. **Accessibility**: Consistent accessibility patterns

## File Structure

```
src/
├── components/
│   └── layout/
│       ├── MainLayout/
│       │   ├── MainLayout.tsx
│       │   └── index.ts
│       └── index.ts
└── screens/
    └── main/
        ├── HomeScreen.tsx          ✅ Updated
        ├── ReadingScreen.tsx       ✅ Updated
        ├── WordListScreen.tsx      ✅ Updated
        ├── ProfileScreen.tsx       ✅ Updated
        └── QuizScreen.tsx          ✅ Updated
```

## Migration Guide

### **Step 1: Import MainLayout**
```typescript
import { MainLayout } from "../../components/layout";
```

### **Step 2: Replace Layout Structure**
```typescript
// Remove this:
<LinearGradient colors={[...]} style={styles.container}>
    <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
            {/* content */}
        </View>
    </ScrollView>
</LinearGradient>

// Replace with this:
<MainLayout backgroundColor="#f8fafc">
    {/* content directly */}
</MainLayout>
```

### **Step 3: Remove Redundant Styles**
```typescript
// Remove these from StyleSheet:
// container: { flex: 1 }
// scrollView: { flex: 1 }
// content: { paddingHorizontal: 24, paddingTop: 64, paddingBottom: 120 }
```

### **Step 4: Test and Adjust**
- Verify layout appears correctly
- Adjust padding if needed using props
- Test scrolling behavior
- Check safe area handling

## Future Enhancements

### **Planned Features**
1. **Theme Integration**: Automatic theme color support
2. **Animation Support**: Built-in page transition animations
3. **Loading States**: Integrated loading overlay support
4. **Error Boundaries**: Built-in error handling
5. **Analytics**: Automatic screen tracking

### **Customization Options**
1. **Header Integration**: Optional header component support
2. **Footer Support**: Built-in footer area management
3. **Sidebar Support**: Optional sidebar layout
4. **Modal Integration**: Built-in modal management

The MainLayout system provides a solid foundation for consistent, maintainable, and user-friendly screen layouts throughout the LingoTexts application.
