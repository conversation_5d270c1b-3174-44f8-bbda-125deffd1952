# DeepL Entegrasyonu

LingoTexts uygulaması artık DeepL API ile entegre edilmiştir. DeepL, dünyanın en kaliteli çeviri servislerinden biridir.

## 🌟 Özellikler

- **<PERSON><PERSON>ks<PERSON>**: DeepL en doğru çevirileri sağlar
- **Hızlı**: Milisaniyeler içinde çeviri
- **<PERSON><PERSON>lli**: 30+ dil desteği
- **Ücretsiz Tier**: Ayda 500.000 karakter ücretsiz
- **Pro Tier**: Sınırs<PERSON>z kullanım ve ek özellikler

## 🚀 Kurulum

### 1. DeepL API Key Alın
1. [DeepL API](https://www.deepl.com/pro-api) sayfasına gidin
2. Ücretsiz hesap oluşturun
3. API anahtarınızı alın

### 2. Çevre Değişkenini Ayarlayın
```bash
# .env dosyasına ekleyin
EXPO_PUBLIC_DEEPL_API_KEY=your_deepl_api_key_here
```

### 3. Ku<PERSON><PERSON>m
```typescript
import { deepLTranslationService } from '../services/translation';

// Kelime çevirisi
const result = await deepLTranslationService.translateWord('hello');
console.log(result.meaning); // "merhaba"

// Metin çevirisi
const text = await deepLTranslationService.translateText('Hello world');
console.log(text); // "Merhaba dünya"
```

## 📱 InteractiveText Kullanımı

```typescript
import { InteractiveText } from '../components/reading';

<InteractiveText
    text="Your English text here"
    fontSize={16}
    lineHeight={24}
    color="#212529"
/>
```

### Nasıl Çalışır?
1. Herhangi bir kelimeye **0.5 saniye** basılı tutun
2. Tooltip açılır ve DeepL ile çeviri başlar
3. Yüksek kaliteli çeviri gösterilir

## 🔧 Konfigürasyon

### Free vs Pro API
```typescript
// Free API (varsayılan)
const freeService = new DeepLTranslationService();

// Pro API
const proService = new DeepLTranslationService("your_pro_api_key", true);
```

### Desteklenen Diller
DeepL şu dilleri destekler:

**Kaynak Diller:**
- 🇺🇸 İngilizce (EN)
- 🇩🇪 Almanca (DE)
- 🇫🇷 Fransızca (FR)
- 🇪🇸 İspanyolca (ES)
- 🇮🇹 İtalyanca (IT)
- 🇵🇹 Portekizce (PT)
- 🇷🇺 Rusça (RU)
- 🇯🇵 Japonca (JA)
- 🇨🇳 Çince (ZH)
- 🇳🇱 Hollandaca (NL)
- 🇵🇱 Lehçe (PL)

**Hedef Diller:**
- 🇹🇷 **Türkçe (TR)** ✅
- Yukarıdaki tüm diller
- 🇧🇬 Bulgarca (BG)
- 🇨🇿 Çekçe (CS)
- 🇩🇰 Danca (DA)
- 🇬🇷 Yunanca (EL)
- 🇪🇪 Estonca (ET)
- 🇫🇮 Fince (FI)
- 🇭🇺 Macarca (HU)
- 🇮🇩 Endonezce (ID)
- 🇱🇻 Letonca (LV)
- 🇱🇹 Litvanca (LT)
- 🇳🇴 Norveççe (NB)
- 🇷🇴 Rumence (RO)
- 🇸🇰 Slovakça (SK)
- 🇸🇮 Slovence (SL)
- 🇸🇪 İsveççe (SV)
- 🇺🇦 Ukraynaca (UK)

## 📊 API Limitleri

### Free Tier
- **500.000 karakter/ay** ücretsiz
- Tüm dil çiftleri
- Standart güvenlik

### Pro Tier
- **Sınırsız** çeviri
- **Daha hızlı** işleme
- **Gelişmiş güvenlik**
- **Formality kontrolü** (resmi/gayri resmi)
- **Glossary desteği**

## 🔄 Fallback Mekanizması

SmartTranslationService şu sırayla dener:

1. **DeepL** - En kaliteli çeviri (birincil)
2. **Google Translate** - Güvenilir alternatif
3. **LibreTranslate** - Ücretsiz seçenek
4. **LibreTranslate Alt1** - Yedek servis
5. **LibreTranslate Alt2** - Yedek servis 2

## 📈 Performans

- **Hız**: Ortalama 200-500ms çeviri süresi
- **Doğruluk**: %95+ doğruluk oranı
- **Kullanılabilirlik**: %99.9+ uptime
- **Dil Kalitesi**: Özellikle Avrupa dilleri için mükemmel

## 🛠️ Gelişmiş Özellikler

### API Kullanım Takibi
```typescript
const usage = await deepLTranslationService.getUsage();
console.log(`Kullanılan: ${usage.character_count}/${usage.character_limit}`);
```

### Desteklenen Dilleri Getir
```typescript
const languages = await deepLTranslationService.getSupportedLanguages();
console.log(languages);
```

### Formality Kontrolü (Pro)
```typescript
// Resmi çeviri
const formal = await deepLTranslationService.translateText(
    "How are you?", 
    "DE", 
    { formality: "more" }
);

// Gayri resmi çeviri
const informal = await deepLTranslationService.translateText(
    "How are you?", 
    "DE", 
    { formality: "less" }
);
```

## 🔒 Güvenlik

- **HTTPS**: Tüm iletişim şifreli
- **API Key**: Güvenli saklama
- **Rate Limiting**: Otomatik hız kontrolü
- **GDPR Uyumlu**: Avrupa veri koruma standartları

## 🐛 Hata Ayıklama

### Yaygın Hatalar

1. **API Key Hatası**
```
Error: DeepL API key not configured
```
**Çözüm**: `.env` dosyasında `EXPO_PUBLIC_DEEPL_API_KEY` ayarlayın

2. **Quota Aşımı**
```
Error: DeepL API error: 456 - Quota exceeded
```
**Çözüm**: Pro hesaba geçin veya ay sonunu bekleyin

3. **Desteklenmeyen Dil**
```
Error: DeepL API error: 400 - Language not supported
```
**Çözüm**: Desteklenen dil kodlarını kontrol edin

### Debug Modu
```typescript
// Console logları aktif
console.log('DeepL translation request:', { text, targetLang });
console.log('DeepL translation response:', result);
```

## 💰 Fiyatlandırma

### Free
- **0€/ay**
- 500.000 karakter/ay
- Tüm dil çiftleri

### Pro
- **5.99€/ay** (1 milyon karakter)
- **22.99€/ay** (sınırsız)
- Gelişmiş özellikler
- Öncelikli destek

## 📞 Destek

- **Dokümantasyon**: https://www.deepl.com/docs-api
- **API Referansı**: https://www.deepl.com/docs-api/translate
- **Destek**: <EMAIL>
- **Status**: https://status.deepl.com

## 🎯 En İyi Pratikler

1. **API Key Güvenliği**: Asla client-side'da expose etmeyin
2. **Rate Limiting**: Çok fazla istek göndermeyin
3. **Caching**: Aynı çevirileri cache'leyin
4. **Error Handling**: Fallback mekanizması kullanın
5. **Monitoring**: API kullanımınızı takip edin
