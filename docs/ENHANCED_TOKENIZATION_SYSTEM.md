# Enhanced Tokenization System for InteractiveText

## Overview

The InteractiveText component now features a comprehensive tokenization system that provides better text processing, formatting preservation, and language support for both Turkish and English content.

## Key Improvements

### 1. **Enhanced Token Structure** ✅

**Before:**
```typescript
interface Token {
    id: string;
    word: string;
    isWord: boolean;
}
```

**After:**
```typescript
interface Token {
    id: string;
    word: string;
    isWord: boolean;
    type: 'word' | 'punctuation' | 'whitespace' | 'line-break' | 'paragraph-break';
    originalIndex: number;
}
```

### 2. **Comprehensive Text Processing** ✅

#### **Multi-Level Processing:**
1. **Paragraph Level**: Identifies double line breaks (`\n\n`) for paragraph separation
2. **Line Level**: Handles single line breaks (`\n`) for line separation
3. **Word Level**: Processes words, punctuation, and whitespace within lines

#### **Enhanced Regex Patterns:**
```typescript
// Turkish and English words including contractions and hyphenated words
const tokenRegex = /([\wçğıöşüÇĞIİÖŞÜ]+(?:[''-][\wçğıöşüÇĞIİÖŞÜ]+)*)|(\s+)|([.,;:!?"""''()[\]{}\-–—…]+)/g;
```

### 3. **Language Support** ✅

#### **Turkish Character Support:**
- Full support for Turkish characters: `çğıöşüÇĞIİÖŞÜ`
- Proper handling of Turkish contractions and compound words
- Accurate word boundary detection for Turkish text

#### **English Language Features:**
- Contractions: `don't`, `won't`, `I'll`, etc.
- Hyphenated words: `state-of-the-art`, `twenty-one`, etc.
- Compound words with apostrophes: `McDonald's`, `children's`, etc.

### 4. **Advanced Whitespace Handling** ✅

#### **Whitespace Types:**
- **Single spaces**: Regular word separation
- **Multiple spaces**: Preserved for formatting
- **Tabs**: Maintained for indentation
- **Line breaks**: Single `\n` for line separation
- **Paragraph breaks**: Double `\n\n` for paragraph separation

#### **Formatting Preservation:**
```typescript
// Paragraph breaks create visual separation
case 'paragraph-break':
    return <View style={styles.paragraphBreak}>
        <Text>{'\n\n'}</Text>
    </View>;

// Line breaks maintain text flow
case 'line-break':
    return <View style={styles.lineBreak}>
        <Text>{'\n'}</Text>
    </View>;
```

### 5. **Improved Punctuation Handling** ✅

#### **Supported Punctuation:**
- Basic: `. , ; : ! ?`
- Quotes: `" " ' ' " '`
- Brackets: `( ) [ ] { }`
- Dashes: `- – —`
- Special: `…` (ellipsis)

#### **Punctuation Processing:**
- Separated from words for better interaction
- Maintains proper spacing and formatting
- Preserves original text appearance

## Technical Implementation

### **Tokenization Process:**

```typescript
const tokenizeText = (inputText: string): Array<Token> => {
    // 1. Process paragraph breaks first
    const paragraphRegex = /\n\s*\n/g;
    
    // 2. Process each text segment
    const processTextSegment = (text: string, baseOffset: number) => {
        // Handle line breaks within segments
        const lineBreakRegex = /\n/g;
        
        // 3. Process individual lines
        const processTextLine = (line: string, baseOffset: number) => {
            // Tokenize words, punctuation, and whitespace
            const tokenRegex = /([\wçğıöşüÇĞIİÖŞÜ]+(?:[''-][\wçğıöşüÇĞIİÖŞÜ]+)*)|(\s+)|([.,;:!?"""''()[\]{}\-–—…]+)/g;
        };
    };
};
```

### **Token ID Generation:**

```typescript
// Word tokens
id: `word_${absoluteIndex}_${fullMatch}`

// Punctuation tokens  
id: `punct_${absoluteIndex}_${encodeURIComponent(fullMatch)}`

// Whitespace tokens
id: `space_${absoluteIndex}`

// Line break tokens
id: `line_${baseOffset + lineMatch.index}`

// Paragraph break tokens
id: `paragraph_${paragraphMatch.index}`
```

### **Rendering Logic:**

```typescript
switch (token.type) {
    case 'word':
        return <TouchableOpacity onLongPress={() => handleWordPressIn(token)}>
            <Text>{token.word}</Text>
        </TouchableOpacity>;
        
    case 'punctuation':
        return <Text style={styles.punctuation}>{token.word}</Text>;
        
    case 'whitespace':
        return <Text style={styles.whitespace}>{token.word}</Text>;
        
    case 'line-break':
        return <View style={styles.lineBreak}>
            <Text>{'\n'}</Text>
        </View>;
        
    case 'paragraph-break':
        return <View style={styles.paragraphBreak}>
            <Text>{'\n\n'}</Text>
        </View>;
}
```

## Performance Considerations

### **Efficiency Optimizations:**
1. **Single Pass Processing**: Text is processed in one pass through the tokenization pipeline
2. **Regex Optimization**: Efficient regex patterns for different text elements
3. **Memory Management**: Tokens store minimal necessary data
4. **Async Processing**: Maintains compatibility with existing async loading system

### **Scalability:**
- Handles large texts efficiently
- Maintains responsive UI during processing
- Compatible with existing performance optimizations

## Benefits

### **User Experience:**
1. **Better Text Interaction**: More accurate word selection for translation
2. **Preserved Formatting**: Original text layout maintained
3. **Language Support**: Works seamlessly with Turkish and English
4. **Professional Appearance**: Clean, properly formatted text display

### **Developer Experience:**
1. **Comprehensive Token Data**: Rich information for each text element
2. **Flexible Rendering**: Easy to customize different token types
3. **Maintainable Code**: Clear separation of concerns
4. **Extensible System**: Easy to add new token types or features

### **Technical Benefits:**
1. **Accurate Word Boundaries**: Better translation target selection
2. **Format Preservation**: Maintains original document structure
3. **Language Agnostic**: Supports multiple languages with proper character sets
4. **Performance Optimized**: Efficient processing without UI blocking

## Usage Examples

### **Basic Text Processing:**
```typescript
const text = "Hello world! This is a test.\n\nNew paragraph here.";
const tokens = tokenizeText(text);
// Results in proper word, punctuation, line-break, and paragraph-break tokens
```

### **Turkish Text Support:**
```typescript
const turkishText = "Merhaba dünya! Bu bir testtir.\n\nYeni paragraf burada.";
const tokens = tokenizeText(turkishText);
// Properly handles Turkish characters and word boundaries
```

### **Complex Formatting:**
```typescript
const complexText = "Don't forget the twenty-one items.\n\n  Indented paragraph here.";
const tokens = tokenizeText(complexText);
// Handles contractions, hyphenated words, and whitespace formatting
```

## Future Enhancements

1. **Additional Languages**: Support for more language-specific character sets
2. **Advanced Punctuation**: Support for language-specific punctuation rules
3. **Semantic Analysis**: Token classification based on linguistic properties
4. **Performance Optimization**: Further optimizations for very large texts
5. **Custom Token Types**: Support for custom token types (URLs, emails, etc.)

The enhanced tokenization system provides a robust foundation for accurate text processing while maintaining excellent performance and user experience.
