{
    "editor.tabSize": 4,
    "editor.detectIndentation": false,
    "prettier.tabWidth": 150,
    // "jest.autoRun": {
    //     "watch": false,
    //     "onSave": "test-file"
    // },
    "search.exclude": {
        "package-lock.json": true
    },
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    "editor.formatOnSave": false,
    "editor.codeActionsOnSave": ["source.addMissingImports", "source.fixAll.eslint"],
    // Overriding multiple language files isn't available on VScode yet
    "[json]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[jsonc]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[postcss]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[scss]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[svelte]": {
        "editor.defaultFormatter": "svelte.svelte-vscode"
    }
}
